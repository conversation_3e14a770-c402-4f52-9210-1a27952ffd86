# Copyright 2023 Huawei Technologies Co., Ltd
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import argparse
from ultralytics import YOL<PERSON>


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--pt', default="yolov8n", help='pt file')
    args = parser.parse_args()

    model = YOLO(args.pt)
    onnx_model = model.export(format="onnx", dynamic=True, simplify=True, opset=11)

# 转为om格式模型
# atc --framework=5 --model=yolov8n.onnx --input_format=NCHW --input_shape="images:${batchsize},3,640,640" --output_type=FP16 --output=yolov8n_bs${batchsize} --log=error --soc_version=Ascend${chip_name}


if __name__ == '__main__':
    main()
