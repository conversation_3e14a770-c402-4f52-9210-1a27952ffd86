---
description: Explore comprehensive data conversion tools for YOLO models including COCO, DOTA, and YOLO bbox2segment converters.
keywords: Ultralytics, data conversion, YOLO models, COCO, DOTA, YOLO bbox2segment, machine learning, annotations
---

# Reference for `ultralytics/data/converter.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/converter.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/converter.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/converter.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.data.converter.coco91_to_coco80_class

<br><br><hr><br>

## ::: ultralytics.data.converter.coco80_to_coco91_class

<br><br><hr><br>

## ::: ultralytics.data.converter.convert_coco

<br><br><hr><br>

## ::: ultralytics.data.converter.convert_segment_masks_to_yolo_seg

<br><br><hr><br>

## ::: ultralytics.data.converter.convert_dota_to_yolo_obb

<br><br><hr><br>

## ::: ultralytics.data.converter.min_index

<br><br><hr><br>

## ::: ultralytics.data.converter.merge_multi_segment

<br><br><hr><br>

## ::: ultralytics.data.converter.yolo_bbox2segment

<br><br><hr><br>

## ::: ultralytics.data.converter.create_synthetic_coco_dataset

<br><br>
