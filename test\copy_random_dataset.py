#!/usr/bin/env python3
"""
随机复制数据集脚本
从datasets/1目录中随机选择100张图片及其对应的标签文件，复制到datasets/4目录
保持原有的目录结构：images/train, images/val, labels/train, labels/val
"""

import os
import random
import shutil
from pathlib import Path
import argparse
from collections import defaultdict

def get_image_files(directory):
    """获取目录下的所有图片文件"""
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []
    
    if directory.exists():
        for file_path in directory.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                image_files.append(file_path)
    
    return image_files

def find_corresponding_label(image_path, labels_dir):
    """根据图片路径找到对应的标签文件"""
    # 将图片文件名改为.txt扩展名
    label_name = image_path.stem + '.txt'
    label_path = labels_dir / label_name
    
    return label_path if label_path.exists() else None

def copy_random_dataset(source_dir, target_dir, num_samples=100):
    """
    从源目录随机复制指定数量的图片和标签到目标目录
    
    Args:
        source_dir: 源数据集目录 (datasets/1)
        target_dir: 目标数据集目录 (datasets/4)
        num_samples: 要复制的样本数量
    """
    
    source_path = Path(source_dir)
    target_path = Path(target_dir)
    
    # 检查源目录是否存在
    if not source_path.exists():
        print(f"❌ 源目录不存在: {source_path}")
        return False
    
    # 创建目标目录结构
    target_images_train = target_path / "images" / "train"
    target_images_val = target_path / "images" / "val"
    target_labels_train = target_path / "labels" / "train"
    target_labels_val = target_path / "labels" / "val"
    
    for dir_path in [target_images_train, target_images_val, target_labels_train, target_labels_val]:
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"📁 创建目录: {dir_path}")
    
    # 收集所有图片文件
    all_images = []
    
    # 从train目录收集图片
    train_images_dir = source_path / "images" / "train"
    train_labels_dir = source_path / "labels" / "train"
    
    if train_images_dir.exists():
        train_images = get_image_files(train_images_dir)
        for img in train_images:
            label_path = find_corresponding_label(img, train_labels_dir)
            if label_path:  # 只添加有对应标签的图片
                all_images.append(('train', img, label_path))
        print(f"📊 train目录找到 {len(train_images)} 张图片，其中 {len([x for x in all_images if x[0] == 'train'])} 张有对应标签")
    
    # 从val目录收集图片
    val_images_dir = source_path / "images" / "val"
    val_labels_dir = source_path / "labels" / "val"
    
    if val_images_dir.exists():
        val_images = get_image_files(val_images_dir)
        val_count_before = len(all_images)
        for img in val_images:
            label_path = find_corresponding_label(img, val_labels_dir)
            if label_path:  # 只添加有对应标签的图片
                all_images.append(('val', img, label_path))
        val_count = len(all_images) - val_count_before
        print(f"📊 val目录找到 {len(val_images)} 张图片，其中 {val_count} 张有对应标签")
    
    if not all_images:
        print("❌ 没有找到任何有效的图片-标签对")
        return False
    
    print(f"📊 总共找到 {len(all_images)} 个有效的图片-标签对")
    
    # 随机选择指定数量的样本
    if len(all_images) < num_samples:
        print(f"⚠️  可用样本数 ({len(all_images)}) 少于请求数量 ({num_samples})，将复制所有可用样本")
        selected_samples = all_images
    else:
        selected_samples = random.sample(all_images, num_samples)
    
    print(f"🎯 随机选择了 {len(selected_samples)} 个样本进行复制")
    
    # 统计选择的样本分布
    train_count = len([x for x in selected_samples if x[0] == 'train'])
    val_count = len([x for x in selected_samples if x[0] == 'val'])
    print(f"📈 选择分布: train={train_count}, val={val_count}")
    
    # 复制文件
    copied_count = 0
    failed_count = 0
    
    for split, image_path, label_path in selected_samples:
        try:
            # 确定目标路径
            if split == 'train':
                target_img_dir = target_images_train
                target_label_dir = target_labels_train
            else:
                target_img_dir = target_images_val
                target_label_dir = target_labels_val
            
            # 复制图片
            target_img_path = target_img_dir / image_path.name
            shutil.copy2(image_path, target_img_path)
            
            # 复制标签
            target_label_path = target_label_dir / label_path.name
            shutil.copy2(label_path, target_label_path)
            
            copied_count += 1
            
            if copied_count % 20 == 0:  # 每20个文件显示一次进度
                print(f"📋 已复制 {copied_count}/{len(selected_samples)} 个样本...")
                
        except Exception as e:
            print(f"❌ 复制失败 {image_path.name}: {e}")
            failed_count += 1
    
    print(f"\n✅ 复制完成!")
    print(f"📊 成功复制: {copied_count} 个样本")
    print(f"❌ 失败: {failed_count} 个样本")
    print(f"📁 目标目录: {target_path}")
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='随机复制数据集样本')
    
    parser.add_argument('--source', type=str, default='datasets/1', 
                       help='源数据集目录 (默认: datasets/1)')
    parser.add_argument('--target', type=str, default='datasets/4',
                       help='目标数据集目录 (默认: datasets/4)')
    parser.add_argument('--num_samples', type=int, default=100,
                       help='要复制的样本数量 (默认: 100)')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子 (默认: 42)')
    
    args = parser.parse_args()
    
    # 设置随机种子以确保结果可重现
    random.seed(args.seed)
    
    print("🚀 随机数据集复制工具")
    print("=" * 50)
    print(f"📂 源目录: {args.source}")
    print(f"📂 目标目录: {args.target}")
    print(f"🎯 样本数量: {args.num_samples}")
    print(f"🎲 随机种子: {args.seed}")
    print("=" * 50)
    
    # 执行复制
    success = copy_random_dataset(args.source, args.target, args.num_samples)
    
    if success:
        print("\n🎉 任务完成!")
    else:
        print("\n💥 任务失败!")

if __name__ == "__main__":
    main()
