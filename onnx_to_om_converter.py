#!/usr/bin/env python3
"""
ONNX 模型转换为华为 NPU .om 模型
使用华为 CANN 工具链进行转换
"""

import os
import sys
import argparse
from pathlib import Path
import subprocess
import shutil

def check_cann_environment():
    """检查 CANN 环境是否正确安装"""
    try:
        # 检查 CANN 环境变量
        cann_home = os.environ.get('CANN_HOME')
        if not cann_home:
            print("❌ CANN_HOME 环境变量未设置")
            return False
        
        print(f"✅ CANN_HOME: {cann_home}")
        
        # 检查 atc 工具是否存在
        atc_path = shutil.which('atc')
        if not atc_path:
            print("❌ atc 工具未找到，请确保 CANN 工具链已正确安装")
            return False
        
        print(f"✅ atc 工具路径: {atc_path}")
        
        # 检查 CANN 版本
        try:
            result = subprocess.run(['atc', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ CANN 版本信息: {result.stdout.strip()}")
            else:
                print(f"⚠️  无法获取 CANN 版本信息: {result.stderr}")
        except Exception as e:
            print(f"⚠️  检查 CANN 版本时出错: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查 CANN 环境时出错: {e}")
        return False

def get_npu_soc_version():
    """获取 NPU SoC 版本"""
    try:
        # 尝试从环境变量获取
        soc_version = os.environ.get('ASCEND_DEVICE_ID')
        if soc_version:
            print(f"✅ 从环境变量获取 NPU SoC: {soc_version}")
            return soc_version
        
        # 尝试使用 npu-smi 工具获取
        try:
            result = subprocess.run(['npu-smi', 'info'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # 解析输出获取 SoC 版本
                for line in result.stdout.split('\n'):
                    if 'NPU Name' in line or 'Device Name' in line:
                        soc_version = line.split(':')[-1].strip()
                        print(f"✅ 从 npu-smi 获取 NPU SoC: {soc_version}")
                        return soc_version
        except:
            pass
        
        # 默认值
        default_soc = "Ascend910B"  # 根据实际硬件调整
        print(f"⚠️  无法自动检测 NPU SoC，使用默认值: {default_soc}")
        return default_soc
        
    except Exception as e:
        print(f"❌ 获取 NPU SoC 版本时出错: {e}")
        return "Ascend310"

def create_acl_config(soc_version):
    """创建 ACL 配置文件"""
    config_content = f"""# ACL 配置文件
# NPU SoC 版本
soc_version={soc_version}

# 内存配置
input_format=NCHW
precision_mode=allow_fp32_to_fp16
op_select_implmode=high_performance
optypelist_for_implmode=Conv2D
"""
    
    config_path = "acl_config.txt"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 创建 ACL 配置文件: {config_path}")
    return config_path

def convert_onnx_to_om(onnx_path, output_path, soc_version, precision="FP16"):
    """使用 atc 工具转换 ONNX 模型为 .om 模型"""
    
    # 创建 ACL 配置文件
    acl_config = create_acl_config(soc_version)
    
    # 构建 atc 命令
    cmd = [
        'atc',
        f'--model={onnx_path}',
        f'--output={output_path}',
        f'--soc_version={soc_version}',
        f'--precision_mode={precision}',
        f'--input_format=NCHW',
        f'--op_select_implmode=high_performance',
        f'--optypelist_for_implmode=Conv2D',
        f'--log=info',
        f'--acl_config={acl_config}'
    ]
    
    print("🔧 开始转换 ONNX 模型为 .om 模型...")
    print(f"📥 输入文件: {onnx_path}")
    print(f"📤 输出文件: {output_path}")
    print(f"🎯 NPU SoC: {soc_version}")
    print(f"⚙️  精度模式: {precision}")
    
    print("\n🚀 执行转换命令:")
    print(" ".join(cmd))
    
    try:
        # 执行转换
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 模型转换成功！")
            print(f"📁 输出文件: {output_path}")
            
            # 检查输出文件是否存在
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
                print(f"📊 文件大小: {file_size:.2f} MB")
            else:
                print("⚠️  输出文件未找到")
            
            return True
        else:
            print("❌ 模型转换失败！")
            print(f"错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 转换超时，请检查模型大小和硬件性能")
        return False
    except Exception as e:
        print(f"❌ 转换过程中出错: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(acl_config):
            os.remove(acl_config)

def optimize_om_model(om_path, output_path=None):
    """优化 .om 模型（可选）"""
    if output_path is None:
        output_path = om_path.replace('.om', '_optimized.om')
    
    print(f"🔧 优化 .om 模型: {om_path}")
    
    # 这里可以添加模型优化逻辑
    # 例如：模型剪枝、量化等
    
    print(f"✅ 模型优化完成: {output_path}")
    return output_path

def validate_om_model(om_path):
    """验证 .om 模型"""
    print(f"🔍 验证 .om 模型: {om_path}")
    
    if not os.path.exists(om_path):
        print("❌ .om 文件不存在")
        return False
    
    # 检查文件大小
    file_size = os.path.getsize(om_path)
    if file_size == 0:
        print("❌ .om 文件为空")
        return False
    
    print(f"✅ .om 模型验证通过，文件大小: {file_size / (1024*1024):.2f} MB")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ONNX 模型转换为华为 NPU .om 模型')
    parser.add_argument('--onnx', type=str, required=True, help='ONNX 模型文件路径')
    parser.add_argument('--output', type=str, help='输出 .om 文件路径')
    parser.add_argument('--soc', type=str, help='NPU SoC 版本 (如: Ascend310, Ascend910)')
    parser.add_argument('--precision', type=str, default='FP16', 
                       choices=['FP32', 'FP16', 'INT8'], help='精度模式')
    parser.add_argument('--optimize', action='store_true', help='是否优化模型')
    parser.add_argument('--validate', action='store_true', help='是否验证模型')
    
    args = parser.parse_args()
    
    print("🚀 ONNX 到 .om 模型转换工具")
    print("=" * 60)
    
    # 检查 CANN 环境
    if not check_cann_environment():
        print("\n❌ CANN 环境检查失败，请确保：")
        print("   1. 已安装华为 CANN 工具链")
        print("   2. 设置了正确的环境变量")
        print("   3. atc 工具可用")
        sys.exit(1)
    
    # 检查输入文件
    onnx_path = Path(args.onnx)
    if not onnx_path.exists():
        print(f"❌ ONNX 文件不存在: {onnx_path}")
        sys.exit(1)
    
    print(f"✅ 输入文件存在: {onnx_path}")
    
    # 设置输出路径
    if args.output:
        output_path = args.output
    else:
        output_path = onnx_path.with_suffix('.om')
    
    # 获取 NPU SoC 版本
    soc_version = args.soc if args.soc else get_npu_soc_version()
    
    # 执行转换
    print("\n" + "=" * 60)
    success = convert_onnx_to_om(str(onnx_path), str(output_path), soc_version, args.precision)
    
    if success:
        # 可选：优化模型
        if args.optimize:
            print("\n" + "=" * 60)
            optimized_path = optimize_om_model(str(output_path))
            output_path = optimized_path
        
        # 可选：验证模型
        if args.validate:
            print("\n" + "=" * 60)
            validate_om_model(str(output_path))
        
        print("\n🎉 转换完成！")
        print(f"📁 最终模型文件: {output_path}")
        
    else:
        print("\n❌ 转换失败！")
        print("\n💡 常见问题解决方案：")
        print("   1. 检查 CANN 工具链是否正确安装")
        print("   2. 确认 NPU SoC 版本设置正确")
        print("   3. 检查 ONNX 模型是否兼容")
        print("   4. 查看详细错误日志")
        sys.exit(1)

if __name__ == "__main__":
    main() 