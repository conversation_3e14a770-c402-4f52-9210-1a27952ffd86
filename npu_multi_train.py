#!/usr/bin/env python3
"""
华为NPU多机多卡YOLO训练脚本
支持单机多卡和多机多卡训练
"""

import os
import sys
import time
import json
import logging
import psutil
import argparse
import subprocess
from pathlib import Path
from ultralytics import YOLO
import torch

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def is_distributed_run():
    """检测是否通过torch.distributed.run启动"""
    # 检查关键的分布式环境变量
    rank = os.environ.get('RANK')
    local_rank = os.environ.get('LOCAL_RANK')
    world_size = os.environ.get('WORLD_SIZE')
    master_addr = os.environ.get('MASTER_ADDR')
    master_port = os.environ.get('MASTER_PORT')

    # 如果这些变量都存在且world_size > 1，说明是真正的分布式训练
    distributed = (all([rank is not None, local_rank is not None, world_size is not None]) and
                  int(world_size) > 1)

    if distributed:
        print("🌐 检测到分布式训练环境 (torch.distributed.run)")
        print(f"   RANK = {rank}")
        print(f"   LOCAL_RANK = {local_rank}")
        print(f"   WORLD_SIZE = {world_size}")
        print(f"   MASTER_ADDR = {master_addr}")
        print(f"   MASTER_PORT = {master_port}")
        return True
    else:
        # 检查是否是单进程的torch.distributed.run启动
        if all([rank is not None, local_rank is not None, world_size is not None]):
            print("🔧 检测到单进程分布式环境，使用单机训练模式")
        else:
            print("🔧 单机训练模式")
        return False

def setup_npu_environment():
    """设置NPU训练环境"""
    try:
        import torch_npu
        print(f"✅ torch_npu版本: {torch_npu.__version__}")
        
        # 检查NPU可用性
        if not torch_npu.npu.is_available():
            raise RuntimeError("NPU不可用，请检查驱动和torch_npu安装")
        
        npu_count = torch_npu.npu.device_count()
        print(f"✅ 检测到 {npu_count} 个NPU设备")
        
        # 显示NPU信息
        for i in range(npu_count):
            device_name = torch_npu.npu.get_device_name(i)
            device_props = torch_npu.npu.get_device_properties(i)
            memory_gb = device_props.total_memory / 1024**3
            print(f"   NPU:{i} - {device_name} ({memory_gb:.1f}GB)")
        
        return npu_count
        
    except ImportError:
        raise ImportError("torch_npu未安装，请先安装华为NPU工具包")
    except Exception as e:
        raise RuntimeError(f"NPU环境检查失败: {e}")

def detect_device():
    """
    检测可用的训练设备
    返回: 设备标识符 ('cpu', 'cuda:0', 或 'npu:0')
    """
    try:
        # 首先检查NPU
        try:
            import torch_npu
            if torch_npu.npu.is_available():
                logger.info("发现可用的NPU设备")
                torch_npu.npu.set_device(0)
                return 'npu:0'
        except ImportError:
            logger.warning("未安装torch_npu")
            pass

        # 检查GPU
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            if device_count > 0:
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"使用GPU: {gpu_name}")
                return 'cuda:0'

        logger.info("使用CPU进行训练")
        return 'cpu'
    except Exception as e:
        logger.warning(f"设备检测出错: {e}")
        return 'cpu'

def get_resource_usage():
    """
    获取系统资源使用情况
    返回: 包含CPU、内存、GPU和NPU使用率的字典
    """
    usage = {
        'cpu_usage': psutil.cpu_percent(),
        'memory_usage': psutil.virtual_memory().percent,
        'gpu_usage': 0.0,
        'npu_usage': 0.0,
        'timestamp': time.time()
    }

    # 获取GPU使用率
    try:
        if torch.cuda.is_available():
            usage['gpu_usage'] = float(torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100)
    except:
        pass

    # 获取NPU使用率
    try:
        result = subprocess.check_output(['npu-smi', 'info'], universal_newlines=True)
        for line in result.split('\n'):
            if '910' in line and 'OK' in line:
                parts = line.split('|')
                if len(parts) >= 4:
                    power_info = parts[3].strip()
                    if 'W' in power_info:
                        power = float(power_info.split('W')[0].strip())
                        usage['npu_usage'] = (power / 250.0) * 100  # 假设最大功率250W
    except:
        pass

    return usage

def parse_training_config(config, dataset_path=None, device=None):
    """
    解析训练配置，转换为YOLOv8可接受的参数格式
    """
    print("config", config)
    # 基础训练参数
    train_args = {
        'epochs': int(config['parameters']['epochs']), # 训练轮数
        'device': device or detect_device(), # 训练设备
        'project': 'runs_detect', # 训练结果保存路径
        'name': f'task_{config.get("id", "npu_train")}', # 训练结果保存名称
        'exist_ok': True, # 是否覆盖训练结果
        'plots': False,  # 禁用绘图以提高性能
    }

    # 数据集路径 - 优先使用命令行参数提供的路径
    if dataset_path:
        train_args['data'] = dataset_path
    else:
        # 数据集和验证参数
        dataset_dir = os.path.join('datasets', config['training']['dataset']['name']) # 数据集路径
        train_args['data'] = os.path.join(dataset_dir, 'data.yaml') # 数据集路径

    # 批次大小和学习率
    train_args['batch'] = int(config['parameters'].get('batchSize', 16)) # 批次大小
    train_args['lr0'] = float(config['parameters'].get('learningRate', 0.01)) # 学习率

    # 图像尺寸
    train_args['imgsz'] = int(config['parameters'].get('imageSize', 640)) # 图像尺寸

    # 优化器相关参数
    optimizer = config['otherParams'].get('optimizer', 'SGD').lower() # 优化器
    train_args['optimizer'] = optimizer # 优化器

    if optimizer in ['sgd', 'adam', 'adamw']:
        train_args['momentum'] = float(config['otherParams'].get('momentum', 0.937)) # 动量
        train_args['weight_decay'] = float(config['otherParams'].get('weightDecay', 0.0005)) # 权重衰减

    # 早停和检查点
    if 'earlyStopping' in config['otherParams']:
        train_args['patience'] = int(config['otherParams']['earlyStopping'])

    if 'checkpointFreq' in config['otherParams']:
        train_args['save_period'] = int(config['otherParams']['checkpointFreq'])

    # 混合精度训练
    if config['otherParams'].get('useMixedPrecision', False):
        train_args['amp'] = True  # 自动混合精度

    # 学习率策略
    lr_strategy = config['parameters'].get('learningRateStrategy', '').lower()
    if lr_strategy == '余弦衰减':
        train_args['cos_lr'] = True  # 使用余弦学习率调度

    # 标签平滑
    if 'labelSmoothing' in config['otherParams']:
        train_args['label_smoothing'] = float(config['otherParams']['labelSmoothing'])

    # Dropout
    if 'dropout' in config['otherParams']:
        train_args['dropout'] = float(config['otherParams']['dropout'])

    # 预热步数
    if 'warmupSteps' in config['otherParams']:
        warmup_epochs = int(config['otherParams']['warmupSteps']) // (train_args['batch'] * 100)  # 估算预热轮数
        train_args['warmup_epochs'] = max(1, warmup_epochs)  # 至少1轮
        train_args['warmup_momentum'] = 0.8
        train_args['warmup_bias_lr'] = 0.1

    # 数据加载器配置
    train_args['workers'] = int(config.get('resources', {}).get('workers', 8))

    # 缓存配置
    if config['otherParams'].get('useCache', False):
        train_args['cache'] = True

    return train_args

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='华为NPU多机多卡YOLO训练')

    # 配置文件参数
    parser.add_argument('--config', type=str, default='training_config.json', help='训练配置文件路径')
    parser.add_argument('--task_id', type=str, help='任务ID，用于生成输出目录名')

    # 基本训练参数 (可覆盖配置文件)
    parser.add_argument('--model', type=str, help='模型文件路径，优先级高于配置文件')
    parser.add_argument('--data', type=str, help='数据集配置文件，优先级高于配置文件')
    parser.add_argument('--epochs', type=int, help='训练轮次')
    parser.add_argument('--batch', type=int, help='批次大小')
    parser.add_argument('--imgsz', type=int, help='图像尺寸')
    parser.add_argument('--lr0', type=float, help='初始学习率')

    # 设备配置
    parser.add_argument('--device', type=str, default='auto',
                       help='设备配置: auto(自动), npu:0(单NPU), npu:0,1(多NPU), npu:0,1,2,3(4NPU)')

    # 分布式训练参数 (仅在非torch.distributed.run模式下使用)
    parser.add_argument('--multi-machine', action='store_true', help='启用多机训练')
    parser.add_argument('--master-addr', type=str, default='127.0.0.1', help='主节点IP地址')
    parser.add_argument('--master-port', type=str, default='29500', help='主节点端口')
    parser.add_argument('--world-size', type=int, default=1, help='总进程数(节点数*每节点NPU数)')
    parser.add_argument('--rank', type=int, default=0, help='当前节点在所有节点中的排名')

    # 保存配置
    parser.add_argument('--project', type=str, help='项目目录')
    parser.add_argument('--name', type=str, help='实验名称')
    parser.add_argument('--exist-ok', action='store_true', help='允许覆盖现有结果')

    # 性能优化
    parser.add_argument('--amp', action='store_true', help='启用自动混合精度训练')
    parser.add_argument('--workers', type=int, help='数据加载线程数')
    parser.add_argument('--cache', action='store_true', help='启用数据缓存')

    return parser.parse_args()

def get_device_config(device_arg, npu_count):
    """根据参数获取设备配置"""
    if device_arg == 'auto':
        if npu_count > 1:
            device = f"npu:{','.join(map(str, range(npu_count)))}"
            print(f"🔄 自动配置: 使用所有{npu_count}个NPU - {device}")
        else:
            device = "npu:0"
            print("🔄 自动配置: 使用单个NPU - npu:0")
    else:
        device = device_arg
        print(f"🔧 手动配置: {device}")
    
    return device

def distributed_train(train_args, model_path, device):
    """分布式训练 (通过torch.distributed.run启动)"""
    print(f"✅ 使用ultralytics版本: {YOLO.__module__}")

    # 获取分布式信息
    rank = int(os.environ.get('RANK', 0))
    local_rank = int(os.environ.get('LOCAL_RANK', 0))
    world_size = int(os.environ.get('WORLD_SIZE', 1))
    master_addr = os.environ.get('MASTER_ADDR', 'localhost')
    master_port = os.environ.get('MASTER_PORT', '29500')

    # 只在rank 0显示详细信息
    if rank == 0:
        print("=" * 60)
        print("🌐 启动分布式NPU训练")
        print(f"🌍 世界大小: {world_size}")
        print(f"📍 全局排名: {rank}")
        print(f"📱 本地排名: {local_rank}")
        print(f"🏠 主节点: {master_addr}:{master_port}")
        print(f"💾 设备: {device}")
        print(f"📊 批次大小: {train_args['batch']}")
        print(f"🔄 训练轮次: {train_args['epochs']}")
        print("=" * 60)

    # 初始化分布式进程组
    try:
        import torch.distributed as dist

        # 检查是否已经初始化
        if not dist.is_initialized():
            if rank == 0:
                print("🔧 初始化分布式进程组...")

            # 设置NPU设备
            try:
                import torch_npu
                torch_npu.npu.set_device(local_rank)
                backend = 'hccl'  # 华为NPU使用HCCL后端
                if rank == 0:
                    print(f"🎯 使用NPU后端: {backend}")
            except ImportError:
                backend = 'nccl'  # 如果没有NPU，使用NCCL
                if rank == 0:
                    print(f"⚠️  NPU不可用，使用GPU后端: {backend}")

            # 初始化进程组
            dist.init_process_group(
                backend=backend,
                init_method=f'tcp://{master_addr}:{master_port}',
                world_size=world_size,
                rank=rank
            )

            if rank == 0:
                print("✅ 分布式进程组初始化完成")
        else:
            if rank == 0:
                print("✅ 分布式进程组已经初始化")

    except Exception as e:
        if rank == 0:
            print(f"❌ 分布式初始化失败: {e}")
            print("💡 尝试使用单机训练模式...")
        # 如果分布式初始化失败，回退到单机模式
        return single_machine_train(train_args, model_path, device)

    # 对于多机多卡，设备需要调整为对应local_rank的NPU
    if ',' in device:
        # 多NPU情况下，使用local_rank对应的NPU
        npu_list = device.replace('npu:', '').split(',')
        if local_rank < len(npu_list):
            actual_device = f"npu:{npu_list[local_rank]}"
        else:
            actual_device = f"npu:{local_rank}"
    else:
        # 单NPU情况
        actual_device = f"npu:{local_rank}"

    if rank == 0:
        print(f"🎯 当前进程使用设备: {actual_device}")

    # 创建模型
    model = YOLO(model_path)

    # 更新设备配置
    train_args['device'] = actual_device
    train_args['verbose'] = rank == 0  # 只在rank 0显示详细输出
    train_args['save'] = rank == 0     # 只在rank 0保存模型
    train_args['plots'] = rank == 0    # 只在rank 0生成图表
    train_args['val'] = rank == 0      # 只在rank 0进行验证

    if rank == 0:
        print("🎯 开始分布式训练...")

    try:
        results = model.train(**train_args)

        if rank == 0:
            print("✅ 分布式训练完成！")
            print(f"📈 结果保存在: {model.trainer.save_dir}")

        return results

    except Exception as e:
        if rank == 0:
            print(f"❌ 分布式训练失败: {e}")
        raise
    finally:
        # 清理分布式环境
        try:
            if dist.is_initialized():
                dist.destroy_process_group()
                if rank == 0:
                    print("🧹 分布式进程组已清理")
        except:
            pass

def single_machine_train(train_args, model_path, device):
    """单机训练（单卡或多卡）"""
    print(f"✅ 使用ultralytics版本: {YOLO.__module__}")

    print("=" * 60)
    print("🚀 启动单机NPU训练")
    print(f"📱 设备: {device}")
    print(f"📊 批次大小: {train_args['batch']}")
    print(f"🔄 训练轮次: {train_args['epochs']}")
    print("=" * 60)

    # 创建模型
    model = YOLO(model_path)

    # 添加训练回调函数
    def on_train_epoch_end(trainer):
        """每个训练周期结束时的回调函数"""
        metrics = trainer.metrics
        resource_metrics = get_resource_usage()

        # 从trainer.tloss获取训练损失组件
        if hasattr(trainer, 'tloss') and len(trainer.tloss) >= 3:
            # 将tensor转换为Python的float值
            box_loss = round(float(trainer.tloss[0]), 4)
            cls_loss = round(float(trainer.tloss[1]), 4)
            dfl_loss = round(float(trainer.tloss[2]), 4)
        else:
            # 如果tloss不可用或格式不符，则使用默认值或尝试从metrics获取
            box_loss = metrics.get('train/box_loss', 0.0)
            cls_loss = metrics.get('train/cls_loss', 0.0)
            dfl_loss = metrics.get('train/dfl_loss', 0.0)

        # 合并训练指标和资源使用指标
        combined_metrics = {
            'epoch': trainer.epoch,
            'train/box_loss': box_loss,
            'train/cls_loss': cls_loss,
            'train/dfl_loss': dfl_loss,
            'metrics/precision': metrics.get('metrics/precision(B)', 0.0),
            'metrics/recall': metrics.get('metrics/recall(B)', 0.0),
            'metrics/mAP50': metrics.get('metrics/mAP50(B)', 0.0),
            'metrics/mAP50-95': metrics.get('metrics/mAP50-95(B)', 0.0),
            **resource_metrics
        }

        # 保存指标到文件
        metrics_file = Path(train_args['project']) / train_args['name'] / 'training_metrics.json'
        metrics_file.parent.mkdir(parents=True, exist_ok=True)
        with open(metrics_file, 'a') as f:
            f.write(json.dumps(combined_metrics) + '\n')

    # 注册回调函数
    model.add_callback('on_train_epoch_end', on_train_epoch_end)

    # 更新设备配置
    train_args['device'] = device
    train_args['verbose'] = True
    train_args['save'] = True
    train_args['plots'] = True
    train_args['val'] = True

    print("🎯 开始训练...")
    try:
        results = model.train(**train_args)
    except Exception as e:
        logger.error(f"训练失败: {e}")
        raise

    print("✅ 训练完成！")
    print(f"📈 结果保存在: {model.trainer.save_dir}")
    return results

def multi_machine_train_setup(train_args, model_path, device, args):
    """多机训练设置 (手动模式，不推荐)"""
    print("⚠️  警告：您正在使用手动多机模式，推荐使用torch.distributed.run")
    print("=" * 60)
    print("🌐 配置多机多卡训练")
    print(f"🏠 主节点: {args.master_addr}:{args.master_port}")
    print(f"🔢 总进程数: {args.world_size}")
    print(f"📍 当前节点排名: {args.rank}")
    print(f"📱 设备: {device}")
    print("=" * 60)

    # 设置分布式环境变量
    os.environ['MASTER_ADDR'] = args.master_addr
    os.environ['MASTER_PORT'] = args.master_port
    os.environ['WORLD_SIZE'] = str(args.world_size)
    os.environ['RANK'] = str(args.rank)

    # 设置LOCAL_RANK
    os.environ['LOCAL_RANK'] = '0'

    print("🔧 环境变量设置完成:")
    print(f"   MASTER_ADDR = {os.environ['MASTER_ADDR']}")
    print(f"   MASTER_PORT = {os.environ['MASTER_PORT']}")
    print(f"   WORLD_SIZE = {os.environ['WORLD_SIZE']}")
    print(f"   RANK = {os.environ['RANK']}")
    print(f"   LOCAL_RANK = {os.environ['LOCAL_RANK']}")

    return single_machine_train(train_args, model_path, device)

def print_training_guide():
    """打印训练指南"""
    print("\n" + "=" * 80)
    print("📖 华为NPU多机多卡训练指南")
    print("=" * 80)
    
    print("\n🔹 单机单卡训练:")
    print("   python npu_multi_train.py --device npu:0")
    
    print("\n🔹 单机多卡训练:")
    print("   python npu_multi_train.py --device npu:0,1,2,3")
    
    print("\n🔹 多机多卡训练 (推荐使用torch.distributed.run):")
    print("   # 机器0:")
    print("   python -m torch.distributed.run --nproc_per_node=1 --nnodes=2 \\")
    print("          --node_rank=0 --master_addr=********** --master_port=29500 \\")
    print("          npu_multi_train.py --device npu:0")
    print("   # 机器1:")
    print("   python -m torch.distributed.run --nproc_per_node=1 --nnodes=2 \\")
    print("          --node_rank=1 --master_addr=********** --master_port=29500 \\")
    print("          npu_multi_train.py --device npu:0")
    
    print("\n💡 性能优化建议:")
    print("   - 使用 --amp 启用混合精度训练")
    print("   - 调整 --batch 大小以充分利用NPU内存")
    print("   - 使用 --cache 缓存数据集以提高训练速度")
    print("   - 确保网络带宽足够支持多机通信")
    
    print("\n⚠️  注意事项:")
    print("   - 确保所有机器能互相访问主节点IP")
    print("   - 所有机器的代码和数据集路径要一致")
    print("   - 批次大小必须能被NPU总数整除")
    print("   - 多机训练时只在主节点(rank=0)显示输出")

def main():
    """主函数"""
    print("🚀 华为NPU多机多卡YOLO训练")
    print(f"📁 工作目录: {Path(__file__).parent.absolute()}")

    # 检测是否通过torch.distributed.run启动
    is_distributed = is_distributed_run()

    # 显示ultralytics版本信息
    print(f"\n✅ 使用ultralytics版本: {YOLO.__module__}")

    args = parse_arguments()

    # 读取配置文件
    try:
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✅ 成功加载配置文件: {args.config}")
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {args.config}")
        print("💡 请确保配置文件存在，或使用 --config 参数指定正确路径")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        sys.exit(1)

    try:
        # 检查NPU环境
        print("\n" + "="*50)
        print("🔧 检查NPU环境")
        print("="*50)
        npu_count = setup_npu_environment()

        # 配置设备
        print("\n" + "="*50)
        print("⚙️ 配置训练设备")
        print("="*50)
        device = get_device_config(args.device, npu_count)

        # 解析训练配置
        train_args = parse_training_config(config, args.data, device)

        # 命令行参数覆盖配置文件参数
        if args.model:
            model_path = args.model
        else:
            model_path = config.get('model', {}).get('path', 'yolo11n.pt')

        if args.epochs:
            train_args['epochs'] = args.epochs
        if args.batch:
            train_args['batch'] = args.batch
        if args.imgsz:
            train_args['imgsz'] = args.imgsz
        if args.lr0:
            train_args['lr0'] = args.lr0
        if args.project:
            train_args['project'] = args.project
        if args.name:
            train_args['name'] = args.name
        if args.exist_ok:
            train_args['exist_ok'] = args.exist_ok
        if args.amp:
            train_args['amp'] = args.amp
        if args.workers:
            train_args['workers'] = args.workers
        if args.cache:
            train_args['cache'] = args.cache

        # 如果提供了task_id，更新name
        if args.task_id:
            train_args['name'] = f'task_{args.task_id}'

        print(f"📋 训练配置:")
        print(f"   模型: {model_path}")
        print(f"   数据集: {train_args['data']}")
        print(f"   轮次: {train_args['epochs']}")
        print(f"   批次大小: {train_args['batch']}")
        print(f"   学习率: {train_args['lr0']}")
        print(f"   设备: {device}")

        # 根据运行模式选择训练方式
        if is_distributed:
            # 通过torch.distributed.run启动的分布式训练
            results = distributed_train(train_args, model_path, device)
        elif args.multi_machine:
            # 手动多机训练模式
            results = multi_machine_train_setup(train_args, model_path, device, args)
        else:
            # 单机训练（包括单机多卡）
            # 检查batch size是否合理
            if ',' in device:
                device_count = len(device.replace('npu:', '').split(','))
                if train_args['batch'] % device_count != 0:
                    print(f"⚠️  警告: batch={train_args['batch']} 不能被NPU数量{device_count}整除")
                    suggested_batch = (train_args['batch'] // device_count + 1) * device_count
                    print(f"💡 建议使用 batch={suggested_batch}")

                    response = input("是否使用建议的batch size? (y/n): ")
                    if response.lower() == 'y':
                        train_args['batch'] = suggested_batch
                        print(f"✅ 已调整batch size为: {train_args['batch']}")

            results = single_machine_train(train_args, model_path, device)

        print("\n✅ 所有训练任务完成！")
        if results:
            print(f"📈 训练结果: {results}")

    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        print_training_guide()
        sys.exit(1)

if __name__ == "__main__":
    main() 