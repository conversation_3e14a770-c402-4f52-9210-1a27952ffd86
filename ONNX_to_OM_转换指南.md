# ONNX 到 .om 模型转换指南

本指南介绍如何将 ONNX 模型转换为华为 NPU 的 .om 模型，以便在华为 Ascend 硬件上部署。

## 📋 前置要求

### 1. 硬件要求
- 华为 Ascend NPU 设备（如 Ascend 310、Ascend 910 等）
- 或者支持 CANN 工具链的 x86 环境

### 2. 软件要求
- 华为 CANN 工具链（CANN Toolkit）
- Python 3.7+
- PyTorch（用于模型训练）
- ONNX（用于模型格式）

## 🔧 环境准备

### 1. 安装 CANN 工具链

```bash
# 下载并安装 CANN Toolkit
# 参考华为官方文档：https://www.hiascend.com/software/cann

# 设置环境变量
export CANN_HOME=/usr/local/Ascend/ascend-toolkit/latest
export PATH=$CANN_HOME/compiler/ccec_compiler/bin:$PATH
export PATH=$CANN_HOME/compiler/bin:$PATH
export ASCEND_OPP_PATH=$CANN_HOME/opp
export TOOLCHAIN_HOME=$CANN_HOME/toolkit
export PATH=$TOOLCHAIN_HOME/bin:$PATH
export LD_LIBRARY_PATH=$TOOLCHAIN_HOME/lib64:$LD_LIBRARY_PATH
```

### 2. 验证安装

```bash
# 检查 atc 工具
atc --version

# 检查 CANN 环境
echo $CANN_HOME
```

## 🚀 使用方法

### 方法一：使用转换脚本

```bash
# 基本转换
python onnx_to_om_converter.py --onnx model.onnx --output model.om

# 指定 NPU SoC 版本
python onnx_to_om_converter.py --onnx model.onnx --output model.om --soc Ascend310

# 指定精度模式
python onnx_to_om_converter.py --onnx model.onnx --output model.om --precision FP16

# 完整转换（包含验证）
python onnx_to_om_converter.py --onnx model.onnx --output model.om --validate --optimize
```

### 方法二：完整训练和转换流程

```bash
# 运行完整的训练和转换流程
python train_and_convert.py
```

### 方法三：手动转换

```bash
# 使用 atc 工具直接转换
atc --model=model.onnx \
    --output=model.om \
    --soc_version=Ascend310 \
    --precision_mode=FP16 \
    --input_format=NCHW \
    --op_select_implmode=high_performance \
    --optypelist_for_implmode=Conv2D \
    --log=info
```

## 📝 参数说明

### 转换脚本参数

| 参数 | 说明 | 默认值 | 选项 |
|------|------|--------|------|
| `--onnx` | ONNX 模型文件路径 | 必需 | - |
| `--output` | 输出 .om 文件路径 | 自动生成 | - |
| `--soc` | NPU SoC 版本 | 自动检测 | Ascend310, Ascend910 等 |
| `--precision` | 精度模式 | FP16 | FP32, FP16, INT8 |
| `--optimize` | 是否优化模型 | False | - |
| `--validate` | 是否验证模型 | False | - |

### atc 工具参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--model` | 输入模型文件 | `--model=model.onnx` |
| `--output` | 输出模型文件 | `--output=model.om` |
| `--soc_version` | NPU SoC 版本 | `--soc_version=Ascend310` |
| `--precision_mode` | 精度模式 | `--precision_mode=FP16` |
| `--input_format` | 输入格式 | `--input_format=NCHW` |
| `--op_select_implmode` | 算子选择模式 | `--op_select_implmode=high_performance` |

## 🔍 常见问题

### 1. CANN 环境问题

**问题：** `CANN_HOME 环境变量未设置`

**解决方案：**
```bash
export CANN_HOME=/usr/local/Ascend/ascend-toolkit/latest
export PATH=$CANN_HOME/compiler/bin:$PATH
```

### 2. atc 工具不可用

**问题：** `atc: command not found`

**解决方案：**
```bash
# 检查 CANN 安装
ls $CANN_HOME/compiler/bin/atc

# 重新安装 CANN 工具链
# 参考华为官方安装文档
```

### 3. 模型转换失败

**问题：** 转换过程中出现错误

**解决方案：**
- 检查 ONNX 模型是否兼容
- 确认 NPU SoC 版本设置正确
- 查看详细错误日志
- 尝试不同的精度模式

### 4. 内存不足

**问题：** 转换过程中内存不足

**解决方案：**
- 减少批次大小
- 使用更小的模型
- 增加系统内存
- 使用 INT8 精度模式

## 📊 性能优化

### 1. 精度选择

- **FP32**: 最高精度，但文件大、推理慢
- **FP16**: 平衡精度和性能，推荐使用
- **INT8**: 最高性能，但精度可能下降

### 2. 算子优化

```bash
# 使用高性能算子
--op_select_implmode=high_performance
--optypelist_for_implmode=Conv2D
```

### 3. 内存优化

```bash
# 设置内存配置
--input_format=NCHW
--precision_mode=allow_fp32_to_fp16
```

## 🧪 模型验证

### 1. 文件检查

```bash
# 检查 .om 文件是否存在
ls -la model.om

# 检查文件大小
du -h model.om
```

### 2. 模型信息

```bash
# 使用 atc 工具查看模型信息
atc --model=model.onnx --output=model.om --dump_ge_graph
```

### 3. 推理测试

```python
# 使用 ACL 接口测试推理
import acl
import numpy as np

# 加载模型
model_id, ret = acl.util.load_model("model.om")

# 准备输入数据
input_data = np.random.randn(1, 3, 640, 640).astype(np.float32)

# 执行推理
output = acl.util.inference(model_id, input_data)
```

## 📁 文件结构

```
project/
├── onnx_to_om_converter.py    # 转换脚本
├── train_and_convert.py       # 完整流程脚本
├── models/                    # 模型输出目录
│   ├── yolo_model.onnx       # ONNX 模型
│   └── yolo_model.om         # .om 模型
├── runs/                      # 训练结果
└── README.md                 # 说明文档
```

## 🔗 相关链接

- [华为 CANN 官方文档](https://www.hiascend.com/software/cann)
- [ATC 工具使用指南](https://www.hiascend.com/document)
- [ONNX 模型格式](https://onnx.ai/)
- [华为 Ascend 硬件](https://www.hiascend.com/hardware)

## 📞 技术支持

如果遇到问题，请：

1. 查看错误日志
2. 检查环境配置
3. 参考华为官方文档
4. 联系技术支持团队 