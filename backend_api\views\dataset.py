import os
import datetime
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.parsers import MultiPart<PERSON>ars<PERSON>, FormParser
from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED, HTTP_204_NO_CONTENT, HTTP_400_BAD_REQUEST
from django.db.models import Q
from django.conf import settings
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
import shutil
import logging
from rest_framework.decorators import action
from django.http import FileResponse, Http404
import mimetypes
from rest_framework.permissions import IsAuthenticated
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseRenderer

from backend_api.models.dataset import Dataset
from backend_api.serializers.dataset import DatasetSerializer, DatasetDetailSerializer, DatasetBasicSerializer
from utils.file_util import extract_archive, get_file_size

logger = logging.getLogger(__name__)

class BinaryFileRenderer(BaseRenderer):
    media_type = '*/*'
    format = None
    charset = None
    render_style = 'binary'

    def render(self, data, media_type=None, renderer_context=None):
        return data

class DatasetViewSets(ModelViewSet):
    """
    声明数据集资源类 用户操作:获取数据集信息 更新数据集 删除数据集 创建数据集
    """
    queryset = Dataset.objects.all()
    serializer_class = DatasetSerializer
    parser_classes = [MultiPartParser, FormParser]
    filter_backends = [DjangoFilterBackend]
    permission_classes = [IsAuthenticated]  # 添加默认权限类
    
    def get_queryset(self):
        user = self.request.user
        queryset = Dataset.objects.all()
        
        # 获取过滤参数
        name = self.request.query_params.get('name', '')
        creator_name = self.request.query_params.get('creator_name', '')  # 上传人
        dataset_type = self.request.query_params.get('dataset_type', '')  # 数据集类型
        file_format = self.request.query_params.get('file_format', '')  # 文件格式  
        dataset_status = self.request.query_params.get('dataset_status', '')  # 数据集状态
        start_time = self.request.query_params.get('start_date', '')  # 开始日期
        end_time = self.request.query_params.get('end_date', '')  # 结束日期
        ids = self.request.query_params.get('ids', '')
        
        # 获取字段过滤参数
        fields = self.request.query_params.get('fields', None)
        if fields:
            fields = fields.split(',')
            queryset = queryset.values(*fields)
        
        # 按指定ID过滤
        if ids:
            try:
                # 支持单个ID或逗号分隔的多个ID
                id_list = [int(id_str.strip()) for id_str in ids.split(',') if id_str.strip()]
                if id_list:
                    queryset = queryset.filter(id__in=id_list)
            except ValueError:
                # 如果ID格式不正确，返回空查询集
                return queryset.none()
        
        # 按名称过滤
        if name:
            queryset = queryset.filter(name__icontains=name)
        
        # 按上传人过滤
        if creator_name:
            queryset = queryset.filter(creater__username__icontains=creator_name)
        
        # 按数据集类型过滤
        if dataset_type:
            queryset = queryset.filter(dataset_type=dataset_type)
            
        # 按文件格式过滤
        if file_format:
            # 从文件名中提取扩展名进行过滤
            queryset = queryset.filter(file__endswith=file_format)
        
        # 按状态过滤
        if dataset_status:
            queryset = queryset.filter(dataset_status=dataset_status)
        
        # 按创建时间范围过滤 - 查找 开始时间 <= 创建时间 <= 结束时间 的数据
        if start_time and end_time:
            try:
                # 解析开始日期，设置为当天的开始时间 00:00:00
                start_datetime = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime)
                # 解析结束日期，设置为当天的结束时间 23:59:59
                end_datetime = datetime.datetime.strptime(end_time, '%Y-%m-%d')
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime)
                # 查找在时间范围内的数据
                queryset = queryset.filter(created_at__gte=start_datetime, created_at__lte=end_datetime)
            except ValueError:
                pass
        elif start_time:
            try:
                # 只有开始时间，查找大于等于开始时间的数据
                start_datetime = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime)
                queryset = queryset.filter(created_at__gte=start_datetime)
            except ValueError:
                pass
        elif end_time:
            try:
                # 只有结束时间，查找小于等于结束时间的数据
                end_datetime = datetime.datetime.strptime(end_time, '%Y-%m-%d')
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
                end_datetime = timezone.make_aware(end_datetime)
                queryset = queryset.filter(created_at__lte=end_datetime)
            except ValueError:
                pass
        
        # 非管理员用户只能查看自己的数据集
        if not user.is_staff:
            queryset = queryset.filter(creater=user.id)
            
        return queryset
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.request.query_params.get('fields') == 'name':
            return DatasetBasicSerializer
        if self.action in ['retrieve', 'list']:
            return DatasetDetailSerializer
        return DatasetSerializer
    
    def create(self, request):
        user = request.user
        
        # 创建一个新的字典来存储数据，而不是复制整个request.data
        request_data = {}
        
        # 从request.data中复制需要的字段
        fields_to_copy = ['name', 'description', 'dataset_type', 'dataset_status']
        for field in fields_to_copy:
            if field in request.data:
                request_data[field] = request.data[field]
        
        # 直接设置creater字段值为用户ID
        request_data["creater"] = user.id
        
        # 检查是否有同名数据集
        name = request_data.get("name")
        if name and Dataset.objects.filter(name=name).exists():
            return Response({"err_msg": f"数据集 {name} 已存在", "msg": "对象已存在错误", "code": "object_exists"}, status=HTTP_400_BAD_REQUEST)
        
        # 处理上传文件
        file = request.FILES.get('file')
        if file:
            # 设置文件存储路径
            file_path = os.path.join(settings.DATASET_ROOT, file.name)
            request_data["address"] = file_path  # 初始设置为原始文件路径
            request_data["original_file"] = file_path  # 保存原始文件路径
            
            # 确保目标目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存文件到指定位置
            with open(file_path, 'wb+') as destination:
                for chunk in file.chunks():
                    destination.write(chunk)
                    
            # 计算文件大小
            request_data["size"] = get_file_size(file_path)
            
            # 如果是压缩文件，尝试解压
            if file.name.lower().endswith(('.zip', '.tar', '.tar.gz', '.tgz')):
                extract_path = os.path.splitext(file_path)[0]
                success, result = extract_archive(file_path, extract_path)
                
                if not success:
                    # 如果解压失败，删除已上传的文件
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    return Response({
                        "err_msg": f"文件解压失败: {result}",
                        "msg": "文件处理错误",
                        "code": "extract_failed"
                    }, status=HTTP_400_BAD_REQUEST)
                    
                # 更新数据集地址为解压后的目录
                request_data["address"] = result
        
        seria = self.get_serializer(data=request_data)
        if seria.is_valid():
            # 保存实例，并确保creater字段被设置
            dataset_inst = seria.save()
            
            # 如果creater仍然是null，手动设置并保存
            if not dataset_inst.creater:
                dataset_inst.creater = user
                dataset_inst.save(update_fields=['creater'])
            
            # 重新获取实例数据
            serializer = self.get_serializer(dataset_inst)
            return Response({"data": serializer.data, "msg": "创建数据集成功", "code": 200}, status=HTTP_201_CREATED)
        else:
            # 如果序列化失败，清理已上传的文件
            if file and os.path.exists(file_path):
                os.remove(file_path)
                if os.path.exists(extract_path):
                    shutil.rmtree(extract_path)
                    
            return Response({"err_msg": seria.errors, "msg": "参数错误", "code": 401}, status=HTTP_400_BAD_REQUEST)
    
    def retrieve(self, request, *args, **kwargs):
        dataset = self.get_object()
        serializer = self.get_serializer(dataset)
        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        
        # 分页操作
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response({"data": serializer.data, "code": 200}, status=HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        """删除数据集"""
        try:
            # 获取数据集实例
            instance = self.get_object()
            
            # 记录要删除的文件路径
            file_path = instance.address
            original_file = instance.original_file
            
            # 先删除数据库记录
            self.perform_destroy(instance)
            
            # 删除解压后的目录或文件
            if file_path and os.path.exists(file_path):
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                except Exception as e:
                    logger.error(f"删除数据集文件失败: {str(e)}")
            
            # 删除原始压缩文件（如果存在且与解压目录不同）
            if original_file and original_file != file_path and os.path.exists(original_file):
                try:
                    os.remove(original_file)
                except Exception as e:
                    logger.error(f"删除原始压缩文件失败: {str(e)}")
            
            return Response({
                "msg": "删除成功",
                "code": 200
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"删除数据集失败: {str(e)}")
            return Response({
                "err_msg": f"删除失败: {str(e)}",
                "code": 400
            }, status=HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'], renderer_classes=[BinaryFileRenderer, JSONRenderer])
    def export(self, request, pk=None):
        """导出数据集文件
        
        如果是压缩文件，直接返回原始压缩包
        如果是普通文件/目录，返回原始文件
        """
        try:
            instance = self.get_object()
            
            # 检查权限：只有管理员或创建者可以导出
            if not request.user.is_staff and request.user != instance.creater:
                return Response({
                    "err_msg": "您没有权限导出此数据集",
                    "msg": "权限错误",
                    "code": "permission_denied"
                }, status=HTTP_400_BAD_REQUEST)
            
            # 优先使用原始压缩文件
            file_path = instance.original_file if instance.original_file else instance.address
            
            if not file_path or not os.path.exists(file_path):
                return Response({
                    "err_msg": "数据集文件不存在",
                    "msg": "文件不存在错误",
                    "code": "file_not_found"
                }, status=HTTP_400_BAD_REQUEST)
            
            if os.path.isdir(file_path):
                return Response({
                    "err_msg": "不支持导出目录格式的数据集，请先压缩",
                    "msg": "不支持的操作",
                    "code": "operation_not_supported"
                }, status=HTTP_400_BAD_REQUEST)
            
            # 获取文件名和MIME类型
            file_name = os.path.basename(file_path)
            content_type, _ = mimetypes.guess_type(file_path)
            if not content_type:
                content_type = 'application/octet-stream'
            
            # 打开文件并创建 FileResponse
            file_handle = open(file_path, 'rb')
            response = FileResponse(file_handle, content_type=content_type)
            
            # 设置下载文件名
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            response['Content-Length'] = os.path.getsize(file_path)
            
            # 设置允许的渲染器
            response.accepted_renderer = BinaryFileRenderer()
            response.accepted_media_type = content_type
            response.renderer_context = {}
            
            return response
            
        except Exception as e:
            logger.error(f"导出数据集失败: {str(e)}")
            return Response({
                "err_msg": f"导出失败: {str(e)}",
                "msg": "导出错误",
                "code": "export_failed"
            }, status=HTTP_400_BAD_REQUEST) 