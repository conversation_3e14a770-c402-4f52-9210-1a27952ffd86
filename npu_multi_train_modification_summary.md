# NPU多机多卡训练脚本修改总结

## 修改目标
参考 `utils/train_yolov8.py` 训练脚本，修改 `npu_multi_train.py`，去掉本地源码依赖，直接使用系统安装的 ultralytics。

## 主要修改内容

### 1. 移除本地源码相关功能
- ❌ 删除 `find_ultralytics_source()` 函数
- ❌ 删除 `setup_ultralytics_path()` 函数  
- ❌ 删除 `verify_ultralytics_source()` 函数
- ❌ 删除所有源码路径设置和验证逻辑

### 2. 简化导入和依赖
```python
# 修改前：复杂的路径设置和本地源码导入
setup_ultralytics_path()
# 各种路径验证...

# 修改后：直接导入系统安装的ultralytics
from ultralytics import YOLO
import torch
```

### 3. 添加训练指标保存功能
参考 `utils/train_yolov8.py`，添加了完整的训练指标保存功能：

```python
def on_train_epoch_end(trainer):
    """每个训练周期结束时的回调函数"""
    metrics = trainer.metrics
    resource_metrics = get_resource_usage()
    
    # 从trainer.tloss获取训练损失组件
    if hasattr(trainer, 'tloss') and len(trainer.tloss) >= 3:
        box_loss = round(float(trainer.tloss[0]), 4)
        cls_loss = round(float(trainer.tloss[1]), 4)
        dfl_loss = round(float(trainer.tloss[2]), 4)
    else:
        box_loss = metrics.get('train/box_loss', 0.0)
        cls_loss = metrics.get('train/cls_loss', 0.0)
        dfl_loss = metrics.get('train/dfl_loss', 0.0)
    
    # 合并训练指标和资源使用指标
    combined_metrics = {
        'epoch': trainer.epoch,
        'train/box_loss': box_loss,
        'train/cls_loss': cls_loss,
        'train/dfl_loss': dfl_loss,
        'metrics/precision': metrics.get('metrics/precision(B)', 0.0),
        'metrics/recall': metrics.get('metrics/recall(B)', 0.0),
        'metrics/mAP50': metrics.get('metrics/mAP50(B)', 0.0),
        'metrics/mAP50-95': metrics.get('metrics/mAP50-95(B)', 0.0),
        **resource_metrics
    }
    
    # 保存指标到文件
    metrics_file = Path(args.project) / args.name / 'training_metrics.json'
    metrics_file.parent.mkdir(parents=True, exist_ok=True)
    with open(metrics_file, 'a') as f:
        f.write(json.dumps(combined_metrics) + '\n')
```

### 4. 添加设备检测和资源监控
从 `utils/train_yolov8.py` 移植了以下功能：

- `detect_device()`: 自动检测NPU/GPU/CPU设备
- `get_resource_usage()`: 获取CPU、内存、GPU、NPU使用率

### 5. 简化训练函数
- 移除了复杂的源码验证逻辑
- 保留了分布式训练支持
- 添加了训练指标回调函数注册
- 改进了错误处理

### 6. 保留的核心功能
✅ NPU环境检测和设置
✅ 分布式训练支持 (torch.distributed.run)
✅ 单机多卡训练
✅ 多机多卡训练
✅ 设备自动配置
✅ 批次大小验证
✅ 训练参数配置

## 使用方式

### 单机单卡训练
```bash
python npu_multi_train.py --device npu:0 --data coco8.yaml --epochs 100
```

### 单机多卡训练
```bash
python npu_multi_train.py --device npu:0,1,2,3 --data coco8.yaml --epochs 100
```

### 多机多卡训练
```bash
# 机器0:
python -m torch.distributed.run --nproc_per_node=1 --nnodes=2 \
       --node_rank=0 --master_addr=************* --master_port=29500 \
       npu_multi_train.py --device npu:0 --data coco8.yaml

# 机器1:
python -m torch.distributed.run --nproc_per_node=1 --nnodes=2 \
       --node_rank=1 --master_addr=************* --master_port=29500 \
       npu_multi_train.py --device npu:0 --data coco8.yaml
```

## 优势

1. **简化部署**: 不再依赖本地源码，直接使用pip安装的ultralytics
2. **更好的兼容性**: 与标准ultralytics版本保持一致
3. **完整的监控**: 支持训练指标和资源使用监控
4. **易于维护**: 代码结构更清晰，减少了复杂的路径管理逻辑
5. **标准化**: 与 `utils/train_yolov8.py` 保持一致的设计模式

## 注意事项

- 需要确保系统已安装 `ultralytics` 和 `torch_npu`
- NPU多卡训练仍需要华为NPU驱动和工具包支持
- 训练指标会保存到 `{project}/{name}/training_metrics.json` 文件中
