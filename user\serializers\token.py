from rest_framework.exceptions import ValidationError
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
import logging

from user.models.user import User

logger = logging.getLogger(__name__)

class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        logger.info(f"开始验证登录信息，接收到的数据：username={attrs.get('username')}")
        self.get_user(attrs)
        refresh = self.get_token(self.user)
        refresh.lifetime
        data = {}
        data['refresh'] = str(refresh)
        data['access'] = str(refresh.access_token)
        data['user'] = self.user.username
        data['lifetime'] = refresh.lifetime
        
        # 添加用户头像URL
        if self.user.avatar and hasattr(self.user.avatar, 'url'):
            request = self.context.get('request')
            if request:
                data['avatar_url'] = request.build_absolute_uri(self.user.avatar.url)
            else:
                data['avatar_url'] = self.user.avatar.url
        else:
            data['avatar_url'] = None
            
        # 添加其他用户基本信息
        data['user_id'] = self.user.id
        data['real_name'] = self.user.real_name or self.user.username
        data['is_staff'] = self.user.is_staff
        data['is_active'] = self.user.is_active
        
        logger.info(f"用户 {self.user.username} 登录成功")
        return data

    def get_user(self, attrs):
        try:
            username = attrs.get('username')
            password = attrs.get('password')
            
            if not username or not password:
                logger.error("登录失败：用户名或密码为空")
                raise ValidationError('用户名和密码不能为空')
            
            logger.info(f"尝试查找用户：{username}")
            user_obj = User.objects.filter(username=username).first()
            
            if not user_obj:
                logger.error(f"登录失败：用户 {username} 不存在")
                raise ValidationError('用户不存在')
            
            if not user_obj.check_password(password):
                logger.error(f"登录失败：用户 {username} 密码错误")
                raise ValidationError('密码或用户名错误')
            
            if not user_obj.is_active:
                logger.error(f"登录失败：用户 {username} 账户已停用")
                raise ValidationError('账户已停用')
            
            logger.info(f"用户 {username} 验证通过")
            self.user = user_obj
            
        except Exception as e:
            logger.error(f"登录过程发生异常：{str(e)}")
            raise