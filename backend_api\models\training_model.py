"""
训练模型管理相关的数据模型
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import uuid


class TrainingModel(models.Model):
    """训练产生的模型记录"""
    
    # 导出状态选择
    EXPORT_STATUS_CHOICES = [
        ('pending', '待导出'),
        ('exporting', '导出中'),
        ('completed', '已完成'),
        ('failed', '导出失败'),
    ]
    
    id = models.AutoField(primary_key=True, verbose_name='模型ID')
    task_id = models.IntegerField(verbose_name='训练任务ID', help_text='关联的训练任务ID')
    model_name = models.CharField(max_length=255, verbose_name='模型名称', help_text='任务ID+模型文件名')
    model_path = models.CharField(max_length=500, verbose_name='模型文件路径', help_text='.pt文件的完整路径')
    
    # 模型性能指标
    accuracy = models.FloatField(default=0.0, verbose_name='准确率', help_text='模型准确率 (mAP50)')
    precision = models.FloatField(default=0.0, verbose_name='精度', help_text='模型精度')
    recall = models.FloatField(default=0.0, verbose_name='召回率', help_text='模型召回率')
    
    # 模型性能
    inference_speed = models.FloatField(default=0.0, verbose_name='推理速度', help_text='推理速度 (FPS)')
    inference_time_ms = models.FloatField(default=0.0, verbose_name='推理时间', help_text='单次推理时间 (毫秒)')
    model_size_mb = models.FloatField(default=0.0, verbose_name='模型大小', help_text='模型文件大小 (MB)')
    
    # 导出状态
    export_status = models.CharField(
        max_length=20, 
        choices=EXPORT_STATUS_CHOICES, 
        default='pending',
        verbose_name='导出状态'
    )
    om_model_path = models.CharField(
        max_length=500, 
        blank=True, 
        null=True, 
        verbose_name='OM模型路径',
        help_text='转换后的.om文件路径'
    )
    
    # 额外信息
    num_classes = models.IntegerField(default=0, verbose_name='类别数量', help_text='模型支持的类别数量')
    architecture = models.CharField(max_length=100, blank=True, verbose_name='模型架构', help_text='如YOLOv8n, YOLOv8s等')
    fitness = models.FloatField(default=0.0, verbose_name='适应度分数', help_text='综合评估分数')
    
    # 元数据
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建者')
    created_time = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    updated_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    # 备注信息
    notes = models.TextField(blank=True, verbose_name='备注', help_text='模型相关备注信息')
    validation_error = models.TextField(blank=True, verbose_name='验证错误', help_text='模型验证时的错误信息')
    
    class Meta:
        db_table = 'training_models'
        verbose_name = '训练模型'
        verbose_name_plural = '训练模型'
        ordering = ['-created_time']
        indexes = [
            models.Index(fields=['task_id']),
            models.Index(fields=['created_by']),
            models.Index(fields=['export_status']),
            models.Index(fields=['created_time']),
        ]
    
    def __str__(self):
        return f"{self.model_name} (Task: {self.task_id})"
    
    @property
    def is_exported(self):
        """检查模型是否已导出为OM格式"""
        return self.export_status == 'completed' and bool(self.om_model_path)
    
    @property
    def performance_summary(self):
        """获取性能摘要"""
        return {
            'accuracy': self.accuracy,
            'precision': self.precision,
            'recall': self.recall,
            'inference_speed': self.inference_speed,
            'model_size_mb': self.model_size_mb
        }
    
    def update_metrics(self, metrics_data):
        """更新模型指标"""
        for key, value in metrics_data.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_time = timezone.now()
        self.save()


class ModelExportLog(models.Model):
    """模型导出日志"""
    
    STATUS_CHOICES = [
        ('started', '开始导出'),
        ('processing', '处理中'),
        ('completed', '导出完成'),
        ('failed', '导出失败'),
    ]
    
    id = models.AutoField(primary_key=True)
    training_model = models.ForeignKey(
        TrainingModel, 
        on_delete=models.CASCADE, 
        related_name='export_logs',
        verbose_name='训练模型'
    )
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, verbose_name='导出状态')
    start_time = models.DateTimeField(default=timezone.now, verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    
    # 导出参数
    export_format = models.CharField(max_length=10, default='om', verbose_name='导出格式')
    export_device = models.CharField(max_length=50, default='npu', verbose_name='导出设备')
    
    # 结果信息
    output_path = models.CharField(max_length=500, blank=True, verbose_name='输出路径')
    file_size_mb = models.FloatField(default=0.0, verbose_name='文件大小(MB)')
    
    # 错误信息
    error_message = models.TextField(blank=True, verbose_name='错误信息')
    log_content = models.TextField(blank=True, verbose_name='导出日志')
    
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='操作者')
    
    class Meta:
        db_table = 'model_export_logs'
        verbose_name = '模型导出日志'
        verbose_name_plural = '模型导出日志'
        ordering = ['-start_time']
    
    def __str__(self):
        return f"{self.training_model.model_name} - {self.status}"


class ModelInferenceLog(models.Model):
    """模型推理日志"""
    
    id = models.AutoField(primary_key=True)
    training_model = models.ForeignKey(
        TrainingModel, 
        on_delete=models.CASCADE, 
        related_name='inference_logs',
        verbose_name='训练模型'
    )
    
    # 推理信息
    input_source = models.CharField(max_length=500, verbose_name='输入源', help_text='图片路径或数据源')
    inference_time_ms = models.FloatField(verbose_name='推理时间(毫秒)')
    confidence_threshold = models.FloatField(default=0.5, verbose_name='置信度阈值')
    
    # 结果信息
    detections_count = models.IntegerField(default=0, verbose_name='检测数量')
    result_data = models.JSONField(default=dict, verbose_name='推理结果', help_text='检测结果的JSON数据')
    output_path = models.CharField(max_length=500, blank=True, verbose_name='结果输出路径')
    
    # 元数据
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='操作者')
    created_time = models.DateTimeField(default=timezone.now, verbose_name='推理时间')
    
    class Meta:
        db_table = 'model_inference_logs'
        verbose_name = '模型推理日志'
        verbose_name_plural = '模型推理日志'
        ordering = ['-created_time']
    
    def __str__(self):
        return f"{self.training_model.model_name} - {self.created_time}"
