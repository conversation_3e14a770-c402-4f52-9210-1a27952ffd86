device = "NPU:0,1"

device = str(device).lower()
    
print(f"device: {device}")
    
    
for remove in "cuda:", "none", "(", ")", "[", "]", "'", " ":
    device = device.replace(remove, "")  # to string, 'cuda:0' -> '0' and '(0, 1)' -> '0,1'

cpu = device == "cpu"
mps = device in {"mps", "mps:0"}  # Apple Metal Performance Shaders (MPS)
# npu = device in {"npu", "npu:0"}  # Huawei Ascend NPU
npu = "npu" in device


print(f"cpu: {cpu}")
print(f"mps: {mps}")
print(f"npu: {npu}")

