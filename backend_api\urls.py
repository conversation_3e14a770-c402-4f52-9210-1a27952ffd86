from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON>, DefaultRouter
from backend_api.views.overview import OverViewAPIView, DownloadsAPIView
from backend_api.views.storage import StorageDownloadView, StorageListView, StorageUploadView, StorageCreateDirView, StorageDeleteView
from backend_api.views.task import TaskResumeView, TaskSuspendingView, TaskViewSets, TaskLogView
from backend_api.views.task import TaskReleaseView
from backend_api.views.scenario import ScenarioViewSets, ScenarioDownloadFileView, ScenarioCheckFileView
from backend_api.views.algorithm import AlgorithmViewSets
from backend_api.views.model import ModelViewSets
from backend_api.views.model import ModelDeductionView
from backend_api.views.simulator import SimulatorViewSets
from backend_api.views.deduction import DeductionViewSets
from backend_api.views.evaluation.evaluation import EvaluationViewSets
from backend_api.views.evaluation.evaluation_test import EvaluationTestViewSets
from backend_api.views.policy.policy import PolicyViewSets
from backend_api.views.policy.policy_evaluation import PolicyEvaluationViewSets
from backend_api.views.environment import EnvironmentViewSet
from backend_api.views.dataset import DatasetViewSets
from backend_api.views.emulator import EmulatorViewSets, EmulatorStatsView
from backend_api.views import training
from backend_api.views.training import (
    TrainingStartView,
    TrainingMetricsView,
    TrainingCancelView,
    TrainingProgressStreamView,
    DLTrainingConfigSaveView,
    DLTrainingConfigImportView,
    DLTrainingConfigListView,
    DLTrainingConfigDeleteView,
    DLTrainingConfigExportView
)
from backend_api.views.rl_training import (
    RLTrainingStartView,
    RLTrainingStopView,
    RLTrainingStatusView,
    RLTrainingMetricsView,
    RLTrainingResourcesView,
    RLTrainingConfigSaveView,
    RLTrainingConfigImportView,
    RLTrainingConfigListView,
    RLTrainingTaskListView,
    RLTrainingConfigDeleteView,
    RLTrainingConfigExportView
)


router = SimpleRouter()
# 可以通过router默认路由注册资源
router.register('tasks', TaskViewSets)
router.register('scenarios', ScenarioViewSets)
router.register('algorithms', AlgorithmViewSets)
router.register('models', ModelViewSets)
router.register('simulators', SimulatorViewSets)
router.register('deductions', DeductionViewSets)
router.register('evaluations', EvaluationViewSets)
router.register('evaluation_tests', EvaluationTestViewSets)
router.register('policies', PolicyViewSets)
router.register('policy_evaluations', PolicyEvaluationViewSets)
router.register('environments', EnvironmentViewSet)
router.register('datasets', DatasetViewSets)
router.register('emulators', EmulatorViewSets)


urlpatterns = [
    # 仿真器相关路由 - 放在最前面以确保优先匹配
    path('emulators/stats/', EmulatorStatsView.as_view(), name='emulator-stats'),
    # 任务相关路由
    path('tasks/release/', TaskReleaseView.as_view(), name='task_release'),
    path('tasks/suspending/', TaskSuspendingView.as_view(), name='task_suspending'),
    path('tasks/resume/', TaskResumeView.as_view(), name='task_resume'),
    path('tasks/log/', TaskLogView.as_view(), name='task_log'),
    path('scenarios/download_file', ScenarioDownloadFileView.as_view(), name='scenario_download_file'),
    path('scenarios/check_file', ScenarioCheckFileView.as_view(), name='scenario_check_file'),
    path('models/deduction/', ModelDeductionView.as_view(), name='model_deduction'),
    path('storages/list/', StorageListView.as_view(), name='storage_list'),
    path('storages/upload/', StorageUploadView.as_view(), name='storage_upload'),
    path('storages/createdir/', StorageCreateDirView.as_view(), name='storage_createdir'),
    path('storages/delete/', StorageDeleteView.as_view(), name='storage_delete'),
    path('storages/download/', StorageDownloadView.as_view(), name='storage_download'),
    path('overview/', OverViewAPIView.as_view(), name='overview'),
    path('downloads/', DownloadsAPIView.as_view(), name='downloads'),
   
    # 深度学习训练相关接口
    path('training/start', TrainingStartView.as_view(), name='start_training'),
    path('training/<int:training_id>/metrics', TrainingMetricsView.as_view(), name='get_training_metrics'),
    path('training/<int:training_id>/cancel', TrainingCancelView.as_view(), name='cancel_training'),
    path('training/<int:training_id>/progress', TrainingProgressStreamView.as_view(), name='training_progress_stream'),
   
    # 深度学习配置管理
    path('training/dl/config', DLTrainingConfigSaveView.as_view(), name='dl_config_save'),
    path('training/dl/config/import', DLTrainingConfigImportView.as_view(), name='dl_config_import'),
    path('training/dl/config/list', DLTrainingConfigListView.as_view(), name='dl_config_list'),
    path('training/dl/config/<str:config_id>/delete', DLTrainingConfigDeleteView.as_view(), name='dl_config_delete'),
    path('training/dl/config/<str:config_id>/export', DLTrainingConfigExportView.as_view(), name='dl_config_export'),
   
    # 强化学习训练相关接口
    path('training/rl/start', RLTrainingStartView.as_view(), name='rl_training_start'),
    path('training/rl/<str:training_id>/stop', RLTrainingStopView.as_view(), name='rl_training_stop'),
    path('training/rl/<str:training_id>/status', RLTrainingStatusView.as_view(), name='rl_training_status'),
    path('training/rl/<str:training_id>/metrics', RLTrainingMetricsView.as_view(), name='rl_training_metrics'),
    path('training/rl/<str:training_id>/resources', RLTrainingResourcesView.as_view(), name='rl_training_resources'),
   
    # 强化学习配置管理
    path('training/rl/config', RLTrainingConfigSaveView.as_view(), name='rl_config_save'),
    path('training/rl/config/import', RLTrainingConfigImportView.as_view(), name='rl_config_import'),
    path('training/rl/config/list', RLTrainingConfigListView.as_view(), name='rl_config_list'),
    path('training/rl/config/<str:config_id>/delete', RLTrainingConfigDeleteView.as_view(), name='rl_config_delete'),
    path('training/rl/config/<str:config_id>/export', RLTrainingConfigExportView.as_view(), name='rl_config_export'),
   
    # 强化学习任务管理
    path('training/rl/tasks', RLTrainingTaskListView.as_view(), name='rl_task_list'),
]

# 添加router的URL到urlpatterns
urlpatterns += router.urls
