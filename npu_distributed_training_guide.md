# NPU分布式训练使用指南

## 问题解决

### 错误: "Default process group has not been initialized"

这个错误通常出现在分布式训练环境配置不正确时。已经在代码中添加了自动初始化分布式进程组的功能。

## 正确的使用方式

### 1. 单机单卡训练
```bash
python npu_multi_train.py --config training_config_npu.json --device npu:0
```

### 2. 单机多卡训练
```bash
# 方式1: 直接指定多个NPU
python npu_multi_train.py --config training_config_npu.json --device npu:0,1,2,3

# 方式2: 使用torch.distributed.run (推荐)
python -m torch.distributed.run --nproc_per_node=4 \
       npu_multi_train.py --config training_config_npu.json --device npu:0,1,2,3
```

### 3. 多机多卡训练

#### 使用torch.distributed.run (推荐)

**机器1 (主节点):**
```bash
python -m torch.distributed.run \
    --nproc_per_node=2 \
    --nnodes=2 \
    --node_rank=0 \
    --master_addr=192.168.1.100 \
    --master_port=29500 \
    npu_multi_train.py --config training_config_npu.json --device npu:0,1
```

**机器2 (从节点):**
```bash
python -m torch.distributed.run \
    --nproc_per_node=2 \
    --nnodes=2 \
    --node_rank=1 \
    --master_addr=192.168.1.100 \
    --master_port=29500 \
    npu_multi_train.py --config training_config_npu.json --device npu:0,1
```

#### 手动多机训练 (不推荐)
```bash
# 机器1:
python npu_multi_train.py --config training_config_npu.json \
       --multi-machine --master-addr 192.168.1.100 --master-port 29500 \
       --world-size 2 --rank 0 --device npu:0

# 机器2:
python npu_multi_train.py --config training_config_npu.json \
       --multi-machine --master-addr 192.168.1.100 --master-port 29500 \
       --world-size 2 --rank 1 --device npu:0
```

## 代码修改说明

### 1. 分布式进程组自动初始化
```python
def distributed_train(train_args, model_path, device):
    # 获取分布式环境变量
    rank = int(os.environ.get('RANK', 0))
    local_rank = int(os.environ.get('LOCAL_RANK', 0))
    world_size = int(os.environ.get('WORLD_SIZE', 1))
    
    # 自动初始化分布式进程组
    if not dist.is_initialized():
        try:
            import torch_npu
            torch_npu.npu.set_device(local_rank)
            backend = 'hccl'  # 华为NPU使用HCCL后端
        except ImportError:
            backend = 'nccl'  # GPU使用NCCL后端
        
        dist.init_process_group(
            backend=backend,
            init_method=f'tcp://{master_addr}:{master_port}',
            world_size=world_size,
            rank=rank
        )
```

### 2. 改进的分布式检测
```python
def is_distributed_run():
    """检测是否通过torch.distributed.run启动"""
    rank = os.environ.get('RANK')
    local_rank = os.environ.get('LOCAL_RANK')
    world_size = os.environ.get('WORLD_SIZE')
    
    # 只有当world_size > 1时才是真正的分布式训练
    distributed = (all([rank is not None, local_rank is not None, world_size is not None]) and 
                  int(world_size) > 1)
    
    return distributed
```

### 3. 错误处理和回退机制
```python
try:
    # 尝试分布式训练
    results = model.train(**train_args)
except Exception as e:
    if rank == 0:
        print(f"❌ 分布式训练失败: {e}")
        print("💡 尝试使用单机训练模式...")
    # 回退到单机模式
    return single_machine_train(train_args, model_path, device)
finally:
    # 清理分布式环境
    if dist.is_initialized():
        dist.destroy_process_group()
```

## 环境要求

### NPU环境
- 华为昇腾NPU驱动
- torch_npu 包
- HCCL 通信库

### GPU环境 (备用)
- NVIDIA GPU驱动
- CUDA
- NCCL 通信库

## 故障排除

### 1. 分布式初始化失败
- 检查网络连接和防火墙设置
- 确保所有节点可以访问主节点的IP和端口
- 验证NPU驱动和torch_npu安装

### 2. 设备不可用
- 检查NPU设备状态: `npu-smi info`
- 验证torch_npu安装: `python -c "import torch_npu; print(torch_npu.npu.is_available())"`

### 3. 内存不足
- 减少batch_size
- 启用梯度累积
- 使用混合精度训练

### 4. 网络通信问题
- 检查HCCL环境变量设置
- 验证节点间网络连通性
- 确保使用正确的网络接口

## 性能优化建议

1. **批次大小**: 确保batch_size能被NPU数量整除
2. **数据加载**: 适当设置workers数量，避免数据加载成为瓶颈
3. **混合精度**: 启用AMP以提高训练速度和减少内存使用
4. **网络优化**: 使用高速网络连接，如InfiniBand
5. **存储优化**: 使用高速存储，避免I/O成为瓶颈

## 监控和调试

### 查看训练进度
```bash
# 查看训练日志
tail -f runs_detect/task_*/train.log

# 查看训练指标
cat runs_detect/task_*/training_metrics.json | tail -10
```

### NPU状态监控
```bash
# 查看NPU使用情况
npu-smi info

# 实时监控
watch -n 1 npu-smi info
```

### 分布式状态检查
```bash
# 检查环境变量
env | grep -E "(RANK|WORLD_SIZE|MASTER)"

# 检查进程
ps aux | grep npu_multi_train
```
