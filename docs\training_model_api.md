# 训练模型管理API文档

## 📋 概述

训练模型管理API提供了完整的模型生命周期管理功能，包括模型信息记录、性能指标管理、模型导出和推理等功能。

## 🔗 接口列表

### 1. 模型列表

**接口路径**: `/backend/training/models`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证

#### 查询参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `task_id` | int | 否 | 按任务ID过滤 |
| `export_status` | string | 否 | 按导出状态过滤 (pending/exporting/completed/failed) |
| `architecture` | string | 否 | 按模型架构过滤 |
| `min_accuracy` | float | 否 | 最小准确率 |
| `max_model_size` | float | 否 | 最大模型大小(MB) |
| `search` | string | 否 | 搜索关键词 |
| `page` | int | 否 | 页码 (默认: 1) |
| `page_size` | int | 否 | 每页数量 (默认: 20) |

#### 调用示例

```javascript
// 获取所有模型
fetch('/backend/training/models', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('模型列表:', data.data);
});

// 按任务ID过滤
fetch('/backend/training/models?task_id=123&page=1&page_size=10', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('任务123的模型:', data.data);
});
```

### 2. 创建模型记录

**接口路径**: `/backend/training/models/create`  
**请求方法**: `POST`  
**权限要求**: 需要登录认证

#### 请求参数

```json
{
    "task_id": 123,
    "model_path": "/path/to/model.pt",
    "accuracy": 0.85,
    "precision": 0.82,
    "recall": 0.78,
    "inference_speed": 45.2,
    "inference_time_ms": 22.1,
    "model_size_mb": 14.5,
    "num_classes": 80,
    "architecture": "YOLOv8n",
    "fitness": 0.81,
    "notes": "训练备注信息"
}
```

#### 调用示例

```javascript
const modelData = {
    task_id: 123,
    model_path: '/runs/detect/train/weights/best.pt',
    accuracy: 0.85,
    precision: 0.82,
    recall: 0.78,
    inference_speed: 45.2,
    inference_time_ms: 22.1,
    model_size_mb: 14.5,
    num_classes: 80,
    architecture: 'YOLOv8n',
    fitness: 0.81
};

fetch('/backend/training/models/create', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(modelData)
})
.then(response => response.json())
.then(data => {
    console.log('模型创建成功:', data.data);
});
```

### 3. 模型详情

**接口路径**: `/backend/training/models/{model_id}`  
**请求方法**: `GET/PUT/DELETE`  
**权限要求**: 需要登录认证

#### GET - 获取模型详情

```javascript
fetch('/backend/training/models/123', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('模型详情:', data.data);
});
```

#### PUT - 更新模型信息

```javascript
const updateData = {
    accuracy: 0.87,
    notes: '更新后的备注'
};

fetch('/backend/training/models/123', {
    method: 'PUT',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(updateData)
})
.then(response => response.json())
.then(data => {
    console.log('模型更新成功:', data.data);
});
```

### 4. 模型导出 (.pt转.om)

**接口路径**: `/backend/training/models/{model_id}/export`  
**请求方法**: `POST`  
**权限要求**: 需要登录认证

#### 请求参数

```json
{
    "model_id": 123,
    "export_format": "om",
    "export_device": "npu",
    "optimization_level": "O2"
}
```

#### 调用示例

```javascript
const exportData = {
    model_id: 123,
    export_format: 'om',
    export_device: 'npu',
    optimization_level: 'O2'
};

fetch('/backend/training/models/123/export', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(exportData)
})
.then(response => response.json())
.then(data => {
    console.log('导出任务已启动:', data.data);
});
```

### 5. 模型推理

**接口路径**: `/backend/training/models/{model_id}/inference`  
**请求方法**: `POST`  
**权限要求**: 需要登录认证

#### 请求参数

```json
{
    "model_id": 123,
    "input_source": "/path/to/image.jpg",
    "confidence_threshold": 0.5,
    "iou_threshold": 0.45,
    "max_detections": 1000,
    "save_result": true,
    "return_image": false
}
```

#### 调用示例

```javascript
const inferenceData = {
    model_id: 123,
    input_source: '/path/to/test_image.jpg',
    confidence_threshold: 0.5,
    iou_threshold: 0.45,
    max_detections: 1000,
    save_result: true,
    return_image: true
};

fetch('/backend/training/models/123/inference', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer your_token',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(inferenceData)
})
.then(response => response.json())
.then(data => {
    console.log('推理结果:', data.data.result);
});
```

### 6. 导出日志

**接口路径**: `/backend/training/models/{model_id}/export-logs`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证

```javascript
// 获取特定模型的导出日志
fetch('/backend/training/models/123/export-logs', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('导出日志:', data.data);
});

// 获取所有导出日志
fetch('/backend/training/export-logs', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('所有导出日志:', data.data);
});
```

### 7. 推理日志

**接口路径**: `/backend/training/models/{model_id}/inference-logs`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证

```javascript
// 获取特定模型的推理日志
fetch('/backend/training/models/123/inference-logs', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('推理日志:', data.data);
});
```

## 📊 数据模型

### TrainingModel 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | int | 模型ID |
| `task_id` | int | 关联的训练任务ID |
| `model_name` | string | 模型名称 (任务ID+文件名) |
| `model_path` | string | .pt文件路径 |
| `accuracy` | float | 准确率 (mAP50) |
| `precision` | float | 精度 |
| `recall` | float | 召回率 |
| `inference_speed` | float | 推理速度 (FPS) |
| `inference_time_ms` | float | 推理时间 (毫秒) |
| `model_size_mb` | float | 模型大小 (MB) |
| `export_status` | string | 导出状态 |
| `om_model_path` | string | OM模型路径 |
| `num_classes` | int | 类别数量 |
| `architecture` | string | 模型架构 |
| `fitness` | float | 适应度分数 |
| `created_time` | datetime | 创建时间 |
| `updated_time` | datetime | 更新时间 |

## 🎯 使用流程

1. **训练完成后创建模型记录**
2. **查看模型列表和详情**
3. **导出模型为OM格式**
4. **使用模型进行推理**
5. **查看导出和推理日志**

## ⚠️ 注意事项

1. **模型路径**: 必须是有效的.pt文件路径
2. **权限控制**: 用户只能操作自己创建的模型
3. **导出状态**: 导出中的模型不能重复导出
4. **文件管理**: 删除模型记录不会删除实际文件
5. **日志记录**: 所有操作都会记录详细日志

这些接口提供了完整的训练模型管理功能，支持模型的全生命周期管理！
