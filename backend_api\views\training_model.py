"""
训练模型管理相关的视图
"""

import logging
import json
from pathlib import Path
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone

from backend_api.models.training_model import TrainingModel, ModelExportLog, ModelInferenceLog
from backend_api.serializers.training_model import (
    TrainingModelSerializer,
    TrainingModelCreateSerializer,
    TrainingModelUpdateSerializer,
    ModelExportLogSerializer,
    ModelInferenceLogSerializer,
    ModelExportRequestSerializer,
    ModelInferenceRequestSerializer,
    ModelListFilterSerializer
)

logger = logging.getLogger(__name__)


class TrainingModelListView(APIView):
    """训练模型列表视图"""
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取训练模型列表"""
        try:
            # 验证查询参数
            filter_serializer = ModelListFilterSerializer(data=request.query_params)
            if not filter_serializer.is_valid():
                return Response({
                    'success': False,
                    'message': '查询参数无效',
                    'errors': filter_serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            filters = filter_serializer.validated_data
            
            # 构建查询
            queryset = TrainingModel.objects.filter(created_by=request.user)
            
            # 应用过滤条件
            if 'task_id' in filters:
                queryset = queryset.filter(task_id=filters['task_id'])
            
            if 'export_status' in filters:
                queryset = queryset.filter(export_status=filters['export_status'])
            
            if 'architecture' in filters:
                queryset = queryset.filter(architecture__icontains=filters['architecture'])
            
            if 'min_accuracy' in filters:
                queryset = queryset.filter(accuracy__gte=filters['min_accuracy'])
            
            if 'max_model_size' in filters:
                queryset = queryset.filter(model_size_mb__lte=filters['max_model_size'])
            
            if 'search' in filters:
                search_term = filters['search']
                queryset = queryset.filter(
                    Q(model_name__icontains=search_term) |
                    Q(notes__icontains=search_term) |
                    Q(architecture__icontains=search_term)
                )
            
            # 分页
            page = filters.get('page', 1)
            page_size = filters.get('page_size', 20)
            paginator = Paginator(queryset.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = TrainingModelSerializer(page_obj.object_list, many=True)
            
            return Response({
                'success': True,
                'data': serializer.data,
                'pagination': {
                    'total': paginator.count,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': paginator.num_pages,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取训练模型列表失败: {e}")
            return Response({
                'success': False,
                'message': f'获取模型列表失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingModelDetailView(APIView):
    """训练模型详情视图"""
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request, model_id):
        """获取模型详情"""
        try:
            model = get_object_or_404(
                TrainingModel,
                id=model_id,
                created_by=request.user
            )
            
            serializer = TrainingModelSerializer(model)
            return Response({
                'success': True,
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取模型详情失败: {e}")
            return Response({
                'success': False,
                'message': f'获取模型详情失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request, model_id):
        """更新模型信息"""
        try:
            model = get_object_or_404(
                TrainingModel,
                id=model_id,
                created_by=request.user
            )
            
            serializer = TrainingModelUpdateSerializer(model, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                
                # 返回更新后的数据
                response_serializer = TrainingModelSerializer(model)
                return Response({
                    'success': True,
                    'message': '模型信息更新成功',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'message': '更新参数无效',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"更新模型信息失败: {e}")
            return Response({
                'success': False,
                'message': f'更新模型信息失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, model_id):
        """删除模型"""
        try:
            model = get_object_or_404(
                TrainingModel,
                id=model_id,
                created_by=request.user
            )
            
            model_name = model.model_name
            model.delete()
            
            return Response({
                'success': True,
                'message': f'模型 {model_name} 删除成功'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"删除模型失败: {e}")
            return Response({
                'success': False,
                'message': f'删除模型失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingModelCreateView(APIView):
    """创建训练模型视图"""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """创建新的训练模型记录"""
        try:
            serializer = TrainingModelCreateSerializer(data=request.data)
            if serializer.is_valid():
                # 生成模型名称：任务ID + 模型文件名
                task_id = serializer.validated_data['task_id']
                model_path = serializer.validated_data['model_path']
                model_filename = Path(model_path).stem
                model_name = f"task_{task_id}_{model_filename}"
                
                # 创建模型记录
                model = serializer.save(
                    created_by=request.user,
                    model_name=model_name
                )
                
                # 返回创建的模型信息
                response_serializer = TrainingModelSerializer(model)
                return Response({
                    'success': True,
                    'message': '训练模型创建成功',
                    'data': response_serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'success': False,
                    'message': '创建参数无效',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"创建训练模型失败: {e}")
            return Response({
                'success': False,
                'message': f'创建训练模型失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelExportView(APIView):
    """模型导出视图"""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """导出模型为OM格式"""
        try:
            serializer = ModelExportRequestSerializer(data=request.data)
            if serializer.is_valid():
                model_id = serializer.validated_data['model_id']
                export_format = serializer.validated_data['export_format']
                export_device = serializer.validated_data['export_device']
                optimization_level = serializer.validated_data['optimization_level']
                
                # 获取模型
                model = get_object_or_404(
                    TrainingModel,
                    id=model_id,
                    created_by=request.user
                )
                
                # 检查模型是否已在导出中
                if model.export_status == 'exporting':
                    return Response({
                        'success': False,
                        'message': '模型正在导出中，请稍后再试'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # 创建导出日志
                export_log = ModelExportLog.objects.create(
                    training_model=model,
                    status='started',
                    export_format=export_format,
                    export_device=export_device,
                    created_by=request.user
                )
                
                # 更新模型状态
                model.export_status = 'exporting'
                model.save()
                
                # TODO: 这里添加实际的模型导出逻辑
                # 目前只是创建记录，实际导出逻辑需要后续实现
                logger.info(f"开始导出模型 {model.model_name} 为 {export_format} 格式")
                
                return Response({
                    'success': True,
                    'message': '模型导出任务已启动',
                    'data': {
                        'export_log_id': export_log.id,
                        'model_id': model.id,
                        'export_format': export_format,
                        'status': 'started'
                    }
                }, status=status.HTTP_202_ACCEPTED)
            else:
                return Response({
                    'success': False,
                    'message': '导出参数无效',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"模型导出失败: {e}")
            return Response({
                'success': False,
                'message': f'模型导出失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelInferenceView(APIView):
    """模型推理视图"""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """使用模型进行推理"""
        try:
            serializer = ModelInferenceRequestSerializer(data=request.data)
            if serializer.is_valid():
                model_id = serializer.validated_data['model_id']
                input_source = serializer.validated_data['input_source']
                confidence_threshold = serializer.validated_data['confidence_threshold']
                iou_threshold = serializer.validated_data['iou_threshold']
                max_detections = serializer.validated_data['max_detections']
                save_result = serializer.validated_data['save_result']
                return_image = serializer.validated_data['return_image']

                # 获取模型
                model = get_object_or_404(
                    TrainingModel,
                    id=model_id,
                    created_by=request.user
                )

                # TODO: 这里添加实际的模型推理逻辑
                # 目前只是创建记录，实际推理逻辑需要后续实现
                logger.info(f"开始使用模型 {model.model_name} 进行推理")

                # 模拟推理结果
                mock_result = {
                    'detections': [
                        {
                            'bbox': [100, 100, 200, 200],
                            'confidence': 0.85,
                            'class_id': 0,
                            'class_name': 'person'
                        }
                    ],
                    'inference_time_ms': 25.5,
                    'image_size': [640, 640]
                }

                # 如果需要保存结果，创建推理日志
                if save_result:
                    inference_log = ModelInferenceLog.objects.create(
                        training_model=model,
                        input_source=input_source,
                        inference_time_ms=mock_result['inference_time_ms'],
                        confidence_threshold=confidence_threshold,
                        detections_count=len(mock_result['detections']),
                        result_data=mock_result,
                        created_by=request.user
                    )

                    response_data = {
                        'inference_log_id': inference_log.id,
                        'model_id': model.id,
                        'model_name': model.model_name,
                        'result': mock_result
                    }
                else:
                    response_data = {
                        'model_id': model.id,
                        'model_name': model.model_name,
                        'result': mock_result
                    }

                # 如果需要返回标注图片
                if return_image:
                    response_data['annotated_image'] = None  # TODO: 实现图片标注逻辑

                return Response({
                    'success': True,
                    'message': '推理完成',
                    'data': response_data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'message': '推理参数无效',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"模型推理失败: {e}")
            return Response({
                'success': False,
                'message': f'模型推理失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelExportLogView(APIView):
    """模型导出日志视图"""

    permission_classes = [IsAuthenticated]

    def get(self, request, model_id=None):
        """获取模型导出日志"""
        try:
            if model_id:
                # 获取特定模型的导出日志
                model = get_object_or_404(
                    TrainingModel,
                    id=model_id,
                    created_by=request.user
                )
                logs = ModelExportLog.objects.filter(training_model=model)
            else:
                # 获取用户所有的导出日志
                user_models = TrainingModel.objects.filter(created_by=request.user)
                logs = ModelExportLog.objects.filter(training_model__in=user_models)

            # 分页
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            paginator = Paginator(logs.order_by('-start_time'), page_size)
            page_obj = paginator.get_page(page)

            # 序列化数据
            serializer = ModelExportLogSerializer(page_obj.object_list, many=True)

            return Response({
                'success': True,
                'data': serializer.data,
                'pagination': {
                    'total': paginator.count,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': paginator.num_pages
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取导出日志失败: {e}")
            return Response({
                'success': False,
                'message': f'获取导出日志失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelInferenceLogView(APIView):
    """模型推理日志视图"""

    permission_classes = [IsAuthenticated]

    def get(self, request, model_id=None):
        """获取模型推理日志"""
        try:
            if model_id:
                # 获取特定模型的推理日志
                model = get_object_or_404(
                    TrainingModel,
                    id=model_id,
                    created_by=request.user
                )
                logs = ModelInferenceLog.objects.filter(training_model=model)
            else:
                # 获取用户所有的推理日志
                user_models = TrainingModel.objects.filter(created_by=request.user)
                logs = ModelInferenceLog.objects.filter(training_model__in=user_models)

            # 分页
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            paginator = Paginator(logs.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)

            # 序列化数据
            serializer = ModelInferenceLogSerializer(page_obj.object_list, many=True)

            return Response({
                'success': True,
                'data': serializer.data,
                'pagination': {
                    'total': paginator.count,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': paginator.num_pages
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取推理日志失败: {e}")
            return Response({
                'success': False,
                'message': f'获取推理日志失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
