# 华为NPU多机多卡YOLO训练指南

## 🚨 问题解决方案

您遇到的 `default process group has not been initialized` 错误是由于使用 `torch.distributed.run` 时环境变量配置冲突导致的。已修复的 `npu_multi_train.py` 脚本会自动检测运行模式并正确处理。

## 🔧 修复说明

**原因分析：**
- `torch.distributed.run` 会自动设置 `RANK`、`LOCAL_RANK`、`WORLD_SIZE` 等环境变量
- 原脚本在 `multi_machine_train_setup()` 中重新设置这些变量，导致冲突
- 分布式进程组初始化失败

**解决方案：**
- 新增 `is_distributed_run()` 函数检测分布式环境
- 新增 `distributed_train()` 函数专门处理分布式训练
- 避免重复设置环境变量，使用 PyTorch 自动设置的值

## 🚀 正确的使用方法

### 1. 多机多卡训练 (推荐)

**机器 0 (主节点):**
```bash
python -m torch.distributed.run \
    --nproc_per_node=1 \
    --nnodes=2 \
    --node_rank=0 \
    --master_addr=********** \
    --master_port=29500 \
    npu_multi_train.py --device npu:0 --batch 32 --epochs 100
```

**机器 1 (从节点):**
```bash
python -m torch.distributed.run \
    --nproc_per_node=1 \
    --nnodes=2 \
    --node_rank=1 \
    --master_addr=********** \
    --master_port=29500 \
    npu_multi_train.py --device npu:0 --batch 32 --epochs 100
```

### 2. 参数说明

**torch.distributed.run 参数：**
- `--nproc_per_node=1`: 每个节点启动1个进程
- `--nnodes=2`: 总共2个节点
- `--node_rank=0/1`: 节点排名（主节点=0，从节点=1）
- `--master_addr=**********`: 主节点IP地址
- `--master_port=29500`: 通信端口

**训练脚本参数：**
- `--device npu:0`: 使用NPU设备0
- `--batch 32`: 批次大小（会自动分配到各进程）
- `--epochs 100`: 训练轮次

### 3. 单机多卡训练

```bash
# 直接调用脚本（推荐）
python npu_multi_train.py --device npu:0,1,2,3 --batch 64

# 或使用torch.distributed.run
python -m torch.distributed.run --nproc_per_node=4 \
    npu_multi_train.py --device npu:0,1,2,3 --batch 64
```

### 4. 单机单卡训练

```bash
python npu_multi_train.py --device npu:0 --batch 16
```

## 📋 环境检查清单

运行前请确保：

### 所有机器都满足以下条件：

1. **网络连通性**
   ```bash
   # 在从节点测试主节点连通性
   ping **********
   telnet ********** 29500
   ```

2. **相同的代码和环境**
   ```bash
   # 检查ultralytics源码路径
   ls -la data/ultralytics_v8/ultralytics/
   
   # 检查NPU环境
   python -c "import torch_npu; print(torch_npu.__version__)"
   ```

3. **相同的数据集路径**
   ```bash
   # 确保数据集配置文件存在
   ls -la coco8.yaml  # 或您使用的数据集配置
   ```

4. **NPU设备可用**
   ```bash
   # 检查NPU状态
   npu-smi info
   ```

## 🔍 调试技巧

### 1. 环境变量验证

在脚本开始时，会自动显示检测到的环境变量：
```
🌐 检测到分布式训练环境 (torch.distributed.run)
   RANK = 0
   LOCAL_RANK = 0
   WORLD_SIZE = 2
   MASTER_ADDR = **********
   MASTER_PORT = 29500
```

### 2. 只在主节点显示输出

修复后的脚本只在 `rank=0` 的进程显示详细输出，避免日志混乱：
```python
verbose = rank == 0,  # 只在rank 0显示详细输出
save = rank == 0,     # 只在rank 0保存模型
plots = rank == 0,    # 只在rank 0生成图表
val = rank == 0,      # 只在rank 0进行验证
```

### 3. 设备自动分配

脚本会根据 `local_rank` 自动分配NPU设备：
```python
# 对于 --device npu:0,1,2,3 和 local_rank=1
actual_device = "npu:1"  # 自动使用对应的NPU
```

## ⚠️ 常见问题解决

### 1. 端口已被占用
```
RuntimeError: Address already in use
```

**解决方案：** 更换端口
```bash
--master_port=29501  # 使用不同的端口
```

### 2. 网络不通
```
RuntimeError: [Errno 113] No route to host
```

**解决方案：** 检查防火墙和网络配置
```bash
# 关闭防火墙（临时）
sudo ufw disable

# 或开放特定端口
sudo ufw allow 29500
```

### 3. 批次大小不匹配
```
⚠️ 警告: batch=17 不能被NPU数量2整除
```

**解决方案：** 使用能被总NPU数整除的批次大小
```bash
--batch 16  # 2机×1NPU = 2, 16÷2=8 ✅
--batch 32  # 2机×1NPU = 2, 32÷2=16 ✅
```

### 4. 进程组初始化失败

如果仍遇到初始化错误，检查：
1. 确保使用修复后的 `npu_multi_train.py`
2. 确保所有机器同时启动训练
3. 检查网络连接和端口开放

## 📊 性能优化建议

### 1. 启用混合精度
```bash
python -m torch.distributed.run ... npu_multi_train.py --amp
```

### 2. 优化批次大小
```bash
# 根据NPU内存调整，通常32-64效果较好
--batch 64
```

### 3. 启用数据缓存
```bash
--cache  # 将数据集缓存到内存，加速训练
```

### 4. 调整工作线程数
```bash
--workers 16  # 根据CPU核心数调整
```

## 🎯 完整示例

**两台机器，每台1个NPU，训练YOLOv11n模型：**

**机器0 (**********):**
```bash
python -m torch.distributed.run \
    --nproc_per_node=1 \
    --nnodes=2 \
    --node_rank=0 \
    --master_addr=********** \
    --master_port=29500 \
    npu_multi_train.py \
    --model yolo11n.pt \
    --data coco8.yaml \
    --epochs 100 \
    --batch 32 \
    --device npu:0 \
    --amp \
    --cache \
    --project runs/multi_node \
    --name test_2nodes
```

**机器1 (172.16.2.3):**
```bash
python -m torch.distributed.run \
    --nproc_per_node=1 \
    --nnodes=2 \
    --node_rank=1 \
    --master_addr=********** \
    --master_port=29500 \
    npu_multi_train.py \
    --model yolo11n.pt \
    --data coco8.yaml \
    --epochs 100 \
    --batch 32 \
    --device npu:0 \
    --amp \
    --cache \
    --project runs/multi_node \
    --name test_2nodes
```

训练结果只会在机器0上保存到 `runs/multi_node/test_2nodes/` 目录。

## ✅ 验证成功

如果看到以下输出，说明多机训练启动成功：

```
🌐 检测到分布式训练环境 (torch.distributed.run)
   RANK = 0
   LOCAL_RANK = 0
   WORLD_SIZE = 2
   MASTER_ADDR = **********
   MASTER_PORT = 29500
============================================================
🌐 启动分布式NPU训练
🌍 世界大小: 2
📍 全局排名: 0
📱 本地排名: 0
💾 设备: npu:0
📊 批次大小: 32
🔄 训练轮次: 100
============================================================
🎯 当前进程使用设备: npu:0
🎯 开始分布式训练...
```

现在您可以使用修复后的脚本成功进行多机多卡训练了！ 