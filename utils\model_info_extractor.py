"""
模型信息提取工具
用于从训练产生的模型文件中提取详细信息并创建模型记录
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime
import torch
import cv2
import numpy as np

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent.absolute()
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# 添加本地ultralytics路径
ultralytics_dir = project_root / "data" / "ultralytics_v8"
if ultralytics_dir.exists():
    sys.path.insert(0, str(ultralytics_dir))

logger = logging.getLogger(__name__)


def extract_model_info(model_path, val_data_path=None, device='cpu'):
    """
    从模型文件中提取详细信息
    Args:
        model_path: 模型文件路径 (.pt文件)
        val_data_path: 验证数据集路径 (可选)
        device: 推理设备
    Returns:
        dict: 包含模型信息的字典
    """
    try:
        from ultralytics import YOLO
        import time
        
        # 检查模型文件是否存在
        model_file = Path(model_path)
        if not model_file.exists():
            return {"error": f"模型文件不存在: {model_path}"}
        
        # 获取模型文件信息
        file_size_bytes = model_file.stat().st_size
        file_size_mb = round(file_size_bytes / (1024 * 1024), 2)
        
        # 加载模型
        model = YOLO(model_path)
        
        # 获取模型基本信息
        model_info = {
            "model_path": str(model_path),
            "file_size_bytes": file_size_bytes,
            "file_size_mb": file_size_mb,
            "created_time": datetime.fromtimestamp(model_file.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
            "device": str(device)
        }
        
        # 获取模型架构信息
        try:
            if hasattr(model.model, 'yaml'):
                model_info["architecture"] = model.model.yaml.get('backbone', 'Unknown')
            if hasattr(model.model, 'nc'):
                model_info["num_classes"] = model.model.nc
        except:
            model_info["architecture"] = "Unknown"
            model_info["num_classes"] = 0
        
        # 如果提供了验证数据集，计算准确率等指标
        if val_data_path and Path(val_data_path).exists():
            try:
                print(f"📊 正在验证模型性能...")
                # 运行验证
                results = model.val(data=val_data_path, device=device, verbose=False)
                
                # 提取验证指标
                if hasattr(results, 'box'):
                    metrics = results.box
                    model_info.update({
                        "precision": round(float(metrics.mp), 4),  # mean precision
                        "recall": round(float(metrics.mr), 4),     # mean recall
                        "accuracy": round(float(metrics.map50), 4),   # mAP@0.5 作为准确率
                        "mAP50": round(float(metrics.map50), 4),   # mAP@0.5
                        "mAP50_95": round(float(metrics.map), 4),  # mAP@0.5:0.95
                        "fitness": round(float(metrics.fitness), 4)  # fitness score
                    })
                else:
                    model_info.update({
                        "precision": 0.0,
                        "recall": 0.0,
                        "accuracy": 0.0,
                        "mAP50": 0.0,
                        "mAP50_95": 0.0,
                        "fitness": 0.0
                    })
                    
            except Exception as e:
                print(f"⚠️  验证失败: {e}")
                model_info.update({
                    "precision": 0.0,
                    "recall": 0.0,
                    "accuracy": 0.0,
                    "mAP50": 0.0,
                    "mAP50_95": 0.0,
                    "fitness": 0.0,
                    "validation_error": str(e)
                })
        else:
            model_info.update({
                "precision": 0.0,
                "recall": 0.0,
                "accuracy": 0.0,
                "mAP50": 0.0,
                "mAP50_95": 0.0,
                "fitness": 0.0,
                "note": "未提供验证数据集"
            })
        
        # 测试推理速度
        try:
            print(f"⏱️  正在测试推理速度...")
            # 创建测试图像
            test_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            
            # 预热
            for _ in range(3):
                _ = model(test_img, device=device, verbose=False)
            
            # 测试推理时间
            times = []
            for _ in range(10):
                start_time = time.time()
                _ = model(test_img, device=device, verbose=False)
                end_time = time.time()
                times.append((end_time - start_time) * 1000)  # 转换为毫秒
            
            avg_inference_time = round(np.mean(times), 2)
            fps = round(1000 / avg_inference_time, 1)
            
            model_info.update({
                "inference_time_ms": avg_inference_time,
                "inference_speed": fps,  # FPS
                "inference_speed_note": f"基于640x640图像的平均推理时间"
            })
            
        except Exception as e:
            print(f"⚠️  推理速度测试失败: {e}")
            model_info.update({
                "inference_time_ms": 0.0,
                "inference_speed": 0.0,
                "inference_error": str(e)
            })
        
        return model_info
        
    except Exception as e:
        return {"error": f"获取模型信息失败: {str(e)}"}


def create_model_record_from_training(task_id, model_path, val_data_path=None, device='cpu', user_id=None):
    """
    从训练结果创建模型记录
    Args:
        task_id: 训练任务ID
        model_path: 模型文件路径
        val_data_path: 验证数据集路径
        device: 推理设备
        user_id: 用户ID
    Returns:
        dict: 创建结果
    """
    try:
        # 提取模型信息
        model_info = extract_model_info(model_path, val_data_path, device)
        
        if "error" in model_info:
            return model_info
        
        # 生成模型名称
        model_filename = Path(model_path).stem
        model_name = f"task_{task_id}_{model_filename}"
        
        # 准备创建模型记录的数据
        model_data = {
            "task_id": task_id,
            "model_name": model_name,
            "model_path": str(model_path),
            "accuracy": model_info.get("accuracy", 0.0),
            "precision": model_info.get("precision", 0.0),
            "recall": model_info.get("recall", 0.0),
            "inference_speed": model_info.get("inference_speed", 0.0),
            "inference_time_ms": model_info.get("inference_time_ms", 0.0),
            "model_size_mb": model_info.get("file_size_mb", 0.0),
            "num_classes": model_info.get("num_classes", 0),
            "architecture": model_info.get("architecture", "Unknown"),
            "fitness": model_info.get("fitness", 0.0),
            "notes": f"自动创建于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        }
        
        # 如果有验证错误，添加到备注中
        if "validation_error" in model_info:
            model_data["validation_error"] = model_info["validation_error"]
        
        return {
            "success": True,
            "model_data": model_data,
            "model_info": model_info
        }
        
    except Exception as e:
        logger.error(f"创建模型记录失败: {e}")
        return {"error": f"创建模型记录失败: {str(e)}"}


def scan_training_results(runs_dir, task_id, val_data_path=None, device='cpu'):
    """
    扫描训练结果目录，提取所有模型信息
    Args:
        runs_dir: 训练结果目录
        task_id: 训练任务ID
        val_data_path: 验证数据集路径
        device: 推理设备
    Returns:
        list: 模型信息列表
    """
    models_info = []
    runs_path = Path(runs_dir)
    
    if not runs_path.exists():
        return [{"error": f"训练结果目录不存在: {runs_dir}"}]
    
    # 查找所有.pt文件
    pt_files = list(runs_path.rglob("*.pt"))
    
    if not pt_files:
        return [{"error": f"在 {runs_dir} 中未找到任何.pt模型文件"}]
    
    print(f"📁 找到 {len(pt_files)} 个模型文件")
    
    for pt_file in pt_files:
        print(f"\n🔍 分析模型: {pt_file.name}")
        
        # 创建模型记录数据
        result = create_model_record_from_training(
            task_id=task_id,
            model_path=str(pt_file),
            val_data_path=val_data_path,
            device=device
        )
        
        if result.get("success"):
            models_info.append(result["model_data"])
        else:
            models_info.append({"error": result.get("error", "未知错误"), "file": str(pt_file)})
    
    return models_info


def save_models_info(models_info, output_file="models_info.json"):
    """
    保存模型信息到JSON文件
    Args:
        models_info: 模型信息列表
        output_file: 输出文件路径
    """
    try:
        output_path = Path(output_file)
        
        # 添加分析时间戳
        analysis_data = {
            "analysis_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "total_models": len(models_info),
            "models": models_info
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 模型信息已保存到: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 保存模型信息失败: {e}")
        return False


if __name__ == "__main__":
    # 示例用法
    import argparse
    
    parser = argparse.ArgumentParser(description='提取模型信息')
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--task_id', type=int, required=True, help='训练任务ID')
    parser.add_argument('--val_data', type=str, help='验证数据集路径')
    parser.add_argument('--device', type=str, default='cpu', help='推理设备')
    parser.add_argument('--output', type=str, default='model_info.json', help='输出文件路径')
    
    args = parser.parse_args()
    
    # 提取模型信息
    result = create_model_record_from_training(
        task_id=args.task_id,
        model_path=args.model_path,
        val_data_path=args.val_data,
        device=args.device
    )
    
    if result.get("success"):
        print("✅ 模型信息提取成功")
        save_models_info([result["model_data"]], args.output)
    else:
        print(f"❌ 模型信息提取失败: {result.get('error')}")
