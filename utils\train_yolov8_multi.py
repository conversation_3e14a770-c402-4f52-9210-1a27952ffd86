#!/usr/bin/env python3
"""
华为NPU单机多卡YOLO训练脚本
支持单机多/单卡训练
"""

import os
import sys
import time
import json
import logging
import psutil
import argparse
import subprocess
from pathlib import Path
import torch
import cv2
import numpy as np
from datetime import datetime

# 添加当前目录到Python路径
ultralytics_dir = "/root/siton-data-b496463103254f46976c4ff88ea74bc9/data/ultralytics_v8"
sys.path.insert(0, str(ultralytics_dir))

# 设置环境变量
os.environ["PYTHONPATH"] = str(ultralytics_dir)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def detect_device():
    """
    检测可用的训练设备，优先使用NPU
    返回: 设备标识符 ('cpu', 'cuda:0', 或 'npu:0,1,2,3')
    """
    try:
        # 首先检查NPU
        try:
            import torch_npu
            if torch_npu.npu.is_available():
                npu_count = torch_npu.npu.device_count()
                logger.info(f"发现 {npu_count} 个可用的NPU设备")

                # 根据NPU数量返回设备配置
                if npu_count > 1:
                    device = f"npu:{','.join(map(str, range(npu_count)))}"
                    print(f"🔄 自动配置: 使用所有{npu_count}个NPU - {device}")
                    return device
                else:
                    print("🔄 自动配置: 使用单个NPU - npu:0")
                    return 'npu:0'
        except ImportError:
            logger.warning("未安装torch_npu，跳过NPU检测")
            pass
        except Exception as e:
            logger.warning(f"NPU检测失败: {e}")
            pass

        # 检查GPU
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            if device_count > 0:
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"使用GPU: {gpu_name}")
                if device_count > 1:
                    return f"cuda:{','.join(map(str, range(device_count)))}"
                else:
                    return 'cuda:0'
        logger.info("使用CPU进行训练")
        return 'cpu'
    except Exception as e:
        logger.warning(f"设备检测出错: {e}")
        return 'cpu'

def get_resource_usage():
    """
    获取系统资源使用情况
    返回: 包含CPU、内存、GPU和NPU使用率的字典
    """
    usage = {
        'cpu_usage': psutil.cpu_percent(),
        'memory_usage': psutil.virtual_memory().percent,
        'gpu_usage': 0.0,
        'npu_usage': 0.0,
        'timestamp': time.time()
    }

    # 获取GPU使用率
    try:
        if torch.cuda.is_available():
            usage['gpu_usage'] = float(torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100)
    except:
        pass

    # 获取NPU使用率
    try:
        result = subprocess.check_output(['npu-smi', 'info'], universal_newlines=True)
        for line in result.split('\n'):
            if '910' in line and 'OK' in line:
                parts = line.split('|')
                if len(parts) >= 4:
                    power_info = parts[3].strip()
                    if 'W' in power_info:
                        power = float(power_info.split('W')[0].strip())
                        usage['npu_usage'] = (power / 250.0) * 100  # 假设最大功率250W
    except:
        pass

    return usage

def parse_training_config(config, dataset_path=None, device=None):
    """
    解析训练配置，转换为YOLOv8可接受的参数格式
    """
    print("config", config)
    # 基础训练参数
    train_args = {
        'epochs': int(config['parameters']['epochs']), # 训练轮数
        'device': device or detect_device(), # 训练设备
        'project': 'runs_detect', # 训练结果保存路径
        'name': f'task_{config.get("id", "npu_train")}', # 训练结果保存名称
        'exist_ok': True, # 是否覆盖训练结果
        'plots': False,  # 禁用绘图以提高性能
    }

    # 数据集路径 - 优先使用命令行参数提供的路径
    if dataset_path:
        train_args['data'] = dataset_path
    else:
        # 数据集和验证参数
        dataset_dir = os.path.join('datasets', config['training']['dataset']['name']) # 数据集路径
        train_args['data'] = os.path.join(dataset_dir, 'data.yaml') # 数据集路径

    # 批次大小和学习率
    train_args['batch'] = int(config['parameters'].get('batchSize', 128)) # 批次大小
    train_args['lr0'] = float(config['parameters'].get('learningRate', 0.01)) # 学习率

    # 图像尺寸
    train_args['imgsz'] = int(config['parameters'].get('imageSize', 640)) # 图像尺寸

    # 优化器相关参数
    optimizer = config['otherParams'].get('optimizer', 'SGD').lower() # 优化器
    train_args['optimizer'] = optimizer # 优化器

    if optimizer in ['sgd', 'adam', 'adamw']:
        train_args['momentum'] = float(config['otherParams'].get('momentum', 0.937)) # 动量
        train_args['weight_decay'] = float(config['otherParams'].get('weightDecay', 0.0005)) # 权重衰减

    # 早停和检查点
    if 'earlyStopping' in config['otherParams']:
        train_args['patience'] = int(config['otherParams']['earlyStopping'])

    if 'checkpointFreq' in config['otherParams']:
        train_args['save_period'] = int(config['otherParams']['checkpointFreq'])

    # 混合精度训练
    if config['otherParams'].get('useMixedPrecision', False):
        train_args['amp'] = True  # 自动混合精度

    # 学习率策略
    lr_strategy = config['parameters'].get('learningRateStrategy', '').lower()
    if lr_strategy == '余弦衰减':
        train_args['cos_lr'] = True  # 使用余弦学习率调度

    # 标签平滑
    if 'labelSmoothing' in config['otherParams']:
        train_args['label_smoothing'] = float(config['otherParams']['labelSmoothing'])

    # Dropout
    if 'dropout' in config['otherParams']:
        train_args['dropout'] = float(config['otherParams']['dropout'])

    # 预热步数
    if 'warmupSteps' in config['otherParams']:
        warmup_epochs = int(config['otherParams']['warmupSteps']) // (train_args['batch'] * 100)  # 估算预热轮数
        train_args['warmup_epochs'] = max(1, warmup_epochs)  # 至少1轮
        train_args['warmup_momentum'] = 0.8
        train_args['warmup_bias_lr'] = 0.1

    # 数据加载器配置
    train_args['workers'] = int(config.get('resources', {}).get('workers', 8))

    # 缓存配置
    if config['otherParams'].get('useCache', False):
        train_args['cache'] = True

    return train_args

def setup_npu_environment():
    """检查NPU训练环境"""
    try:
        import torch_npu
        print(f"✅ torch_npu版本: {torch_npu.__version__}")

        # 检查NPU可用性
        if not torch_npu.npu.is_available():
            raise RuntimeError("NPU不可用，请检查驱动和torch_npu安装")

        npu_count = torch_npu.npu.device_count()
        print(f"✅ 检测到 {npu_count} 个NPU设备")

        # 显示NPU信息
        for i in range(npu_count):
            try:
                device_name = torch_npu.npu.get_device_name(i)
                device_props = torch_npu.npu.get_device_properties(i)
                memory_gb = device_props.total_memory / 1024**3
                print(f"   NPU:{i} - {device_name} ({memory_gb:.1f}GB)")
            except Exception as e:
                print(f"   NPU:{i} - 设备信息获取失败: {e}")

        return npu_count

    except ImportError:
        print("⚠️  torch_npu未安装，将使用其他可用设备")
        return 0
    except Exception as e:
        print(f"⚠️  NPU环境检查失败: {e}")
        return 0

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='华为NPU多机多卡YOLO训练')

    # 配置文件参数
    parser.add_argument('--config', type=str, default='training_config.json', help='训练配置文件路径')
    parser.add_argument('--task_id', type=str, help='任务ID，用于生成输出目录名')

    # 基本训练参数 (可覆盖配置文件)
    parser.add_argument('--model', type=str, help='模型文件路径，优先级高于配置文件')
    parser.add_argument('--data', type=str, help='数据集配置文件，优先级高于配置文件')

    return parser.parse_args()

def single_machine_train(train_args, model_path, device):
    """单机训练（单卡或多卡）"""
    from ultralytics import YOLO

    print(f"✅ 使用ultralytics版本: {YOLO.__module__}")

    print("=" * 60)
    print("🚀 启动单机NPU训练")
    print(f"📱 设备: {device}")
    print(f"📊 批次大小: {train_args['batch']}")
    print(f"🔄 训练轮次: {train_args['epochs']}")
    print("=" * 60)

    # 创建模型
    model = YOLO(model_path)

    # 添加训练回调函数
    def on_train_epoch_end(trainer):
        """每个训练周期结束时的回调函数"""
        # 获取资源使用情况
        resource_metrics = get_resource_usage()
        
        # 安全获取metrics，如果为None则使用空字典
        metrics = trainer.metrics if trainer.metrics is not None else {}
        
        # 从trainer.tloss获取训练损失组件
        if hasattr(trainer, 'tloss') and len(trainer.tloss) >= 3:
            # 将tensor转换为Python的float值
            box_loss = round(float(trainer.tloss[0]), 4)
            cls_loss = round(float(trainer.tloss[1]), 4)
            dfl_loss = round(float(trainer.tloss[2]), 4)
        else:
            # 如果tloss不可用，使用默认值
            box_loss = 0.0
            cls_loss = 0.0
            dfl_loss = 0.0

        # 合并训练指标和资源使用指标
        combined_metrics = {
            'epoch': trainer.epoch,
            'train/box_loss': box_loss,
            'train/cls_loss': cls_loss,
            'train/dfl_loss': dfl_loss,
            'metrics/precision': metrics.get('metrics/precision(B)', 0.0),
            'metrics/recall': metrics.get('metrics/recall(B)', 0.0),
            'metrics/mAP50': metrics.get('metrics/mAP50(B)', 0.0),
            'metrics/mAP50-95': metrics.get('metrics/mAP50-95(B)', 0.0),
            **resource_metrics
        }

        # 记录日志
        logger.info(f"Epoch {trainer.epoch} metrics: {combined_metrics}")

        # 保存指标到文件
        try:
            with open("/workspace/training_metrics.json", 'a') as f:
                f.write(json.dumps(combined_metrics) + '\n')
        except Exception as e:
            logger.error(f"保存训练指标失败: {e}")

    # 注册回调函数
    model.add_callback('on_train_epoch_end', on_train_epoch_end)

    # 更新设备配置
    train_args['device'] = device
    train_args['verbose'] = True
    train_args['save'] = True
    train_args['plots'] = True
    train_args['val'] = True
    train_args['save_period'] = 1  # 每轮保存一次

    print("🎯 开始训练...")
    try:
        results = model.train(**train_args)
    except Exception as e:
        logger.error(f"训练失败: {e}")
        raise

    print("✅ 训练完成！")
    print(f"📈 结果保存在: {model.trainer.save_dir}")
    return results

def main():
    """主函数"""
    print("🚀 华为NPU单机多卡YOLO训练")
    print(f"📁 工作目录: {Path(__file__).parent.absolute()}")
    
    # 显示ultralytics版本信息
    try:
        from ultralytics import YOLO
        print(f"\n✅ 使用ultralytics版本: {YOLO.__module__}")
    except ImportError as e:
        print(f"\n❌ ultralytics导入失败: {e}")
        print("请确保ultralytics_v8目录存在且包含完整的源码")
        sys.exit(1)

    args = parse_arguments()

    # 读取配置文件
    try:
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✅ 成功加载配置文件: {args.config}")
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {args.config}")
        print("💡 请确保配置文件存在，或使用 --config 参数指定正确路径")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        sys.exit(1)
    
    try:
        
        # 检查NPU环境
        print("\n" + "="*50)
        print("🔧 检查NPU环境")
        print("="*50)
        setup_npu_environment()
        
        # 配置设备
        print("\n" + "="*50)
        print("⚙️ 配置训练设备")
        print("="*50)
        device = detect_device()  # 自动检测设备

        # 解析训练配置
        train_args = parse_training_config(config, args.data, device)

        # 命令行参数覆盖配置文件参数
        if args.model:  
            model_path = args.model
        else:
            model_path = config.get('model', {}).get('path', 'yolov8n.pt')

        # 如果提供了task_id，更新name
        if args.task_id:
            train_args['name'] = f'task_{args.task_id}'

        print(f"📋 训练配置:")
        print(f"   模型: {model_path}")
        print(f"   数据集: {train_args['data']}")
        print(f"   轮次: {train_args['epochs']}")
        print(f"   批次大小: {train_args['batch']}")
        print(f"   设备: {device}")

        # 启动单机训练
        results = single_machine_train(train_args, model_path, device)

        print("\n✅ 所有训练任务完成！")
        if results:
            print(f"📈 训练结果: {results}")
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def get_model_info(model_path, val_data_path=None, device='cpu'):
    """
    获取模型的详细信息
    Args:
        model_path: 模型文件路径 (.pt文件)
        val_data_path: 验证数据集路径 (可选，用于计算准确率等指标)
        device: 推理设备
    Returns:
        dict: 包含模型信息的字典
    """
    try:
        from ultralytics import YOLO
        import time

        # 检查模型文件是否存在
        model_file = Path(model_path)
        if not model_file.exists():
            return {"error": f"模型文件不存在: {model_path}"}

        # 获取模型文件信息
        file_size_bytes = model_file.stat().st_size
        file_size_mb = round(file_size_bytes / (1024 * 1024), 2)

        # 加载模型
        model = YOLO(model_path)

        # 获取模型基本信息
        model_info = {
            "model_name": model_file.stem,
            "model_path": str(model_path),
            "file_size_bytes": file_size_bytes,
            "file_size_mb": file_size_mb,
            "created_time": datetime.fromtimestamp(model_file.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
            "device": str(device)
        }

        # 获取模型架构信息
        try:
            if hasattr(model.model, 'yaml'):
                model_info["architecture"] = model.model.yaml.get('backbone', 'Unknown')
            if hasattr(model.model, 'nc'):
                model_info["num_classes"] = model.model.nc
        except:
            pass

        # 如果提供了验证数据集，计算准确率等指标
        if val_data_path and Path(val_data_path).exists():
            try:
                print(f"📊 正在验证模型性能...")
                # 运行验证
                results = model.val(data=val_data_path, device=device, verbose=False)

                # 提取验证指标
                if hasattr(results, 'box'):
                    metrics = results.box
                    model_info.update({
                        "precision": round(float(metrics.mp), 4),  # mean precision
                        "recall": round(float(metrics.mr), 4),     # mean recall
                        "mAP50": round(float(metrics.map50), 4),   # mAP@0.5
                        "mAP50_95": round(float(metrics.map), 4),  # mAP@0.5:0.95
                        "fitness": round(float(metrics.fitness), 4)  # fitness score
                    })
                else:
                    model_info.update({
                        "precision": 0.0,
                        "recall": 0.0,
                        "mAP50": 0.0,
                        "mAP50_95": 0.0,
                        "fitness": 0.0
                    })

            except Exception as e:
                print(f"⚠️  验证失败: {e}")
                model_info.update({
                    "precision": 0.0,
                    "recall": 0.0,
                    "mAP50": 0.0,
                    "mAP50_95": 0.0,
                    "fitness": 0.0,
                    "validation_error": str(e)
                })
        else:
            model_info.update({
                "precision": 0.0,
                "recall": 0.0,
                "mAP50": 0.0,
                "mAP50_95": 0.0,
                "fitness": 0.0,
                "note": "未提供验证数据集"
            })

        # 测试推理速度
        try:
            print(f"⏱️  正在测试推理速度...")
            # 创建测试图像
            test_img = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)

            # 预热
            for _ in range(3):
                _ = model(test_img, device=device, verbose=False)

            # 测试推理时间
            times = []
            for _ in range(10):
                start_time = time.time()
                _ = model(test_img, device=device, verbose=False)
                end_time = time.time()
                times.append((end_time - start_time) * 1000)  # 转换为毫秒

            avg_inference_time = round(np.mean(times), 2)
            fps = round(1000 / avg_inference_time, 1)

            model_info.update({
                "inference_time_ms": avg_inference_time,
                "fps": fps,
                "inference_speed_note": f"基于640x640图像的平均推理时间"
            })

        except Exception as e:
            print(f"⚠️  推理速度测试失败: {e}")
            model_info.update({
                "inference_time_ms": 0.0,
                "fps": 0.0,
                "inference_error": str(e)
            })

        return model_info

    except Exception as e:
        return {"error": f"获取模型信息失败: {str(e)}"}


def analyze_training_models(runs_dir, val_data_path=None, device='cpu'):
    """
    分析训练产生的所有模型文件
    Args:
        runs_dir: 训练结果目录 (通常是runs/detect/train*)
        val_data_path: 验证数据集路径
        device: 推理设备
    Returns:
        list: 包含所有模型信息的列表
    """
    models_info = []
    runs_path = Path(runs_dir)

    if not runs_path.exists():
        return [{"error": f"训练结果目录不存在: {runs_dir}"}]

    # 查找所有.pt文件
    pt_files = list(runs_path.rglob("*.pt"))

    if not pt_files:
        return [{"error": f"在 {runs_dir} 中未找到任何.pt模型文件"}]

    print(f"📁 找到 {len(pt_files)} 个模型文件")

    for pt_file in pt_files:
        print(f"\n🔍 分析模型: {pt_file.name}")
        model_info = get_model_info(str(pt_file), val_data_path, device)
        models_info.append(model_info)

    return models_info


def save_models_analysis(models_info, output_file="models_analysis.json"):
    """
    保存模型分析结果到JSON文件
    Args:
        models_info: 模型信息列表
        output_file: 输出文件路径
    """
    try:
        output_path = Path(output_file)

        # 添加分析时间戳
        analysis_data = {
            "analysis_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "total_models": len(models_info),
            "models": models_info
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)

        print(f"📄 模型分析结果已保存到: {output_path}")
        return True

    except Exception as e:
        print(f"❌ 保存分析结果失败: {e}")
        return False


if __name__ == "__main__":
    main()