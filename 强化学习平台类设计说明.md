# 强化学习平台类设计说明

## 1. 类属性表

### 1.1 RLTrainingTask（强化学习训练任务模型）

| 序号 | 名称 | 标识符 | 数据类型 | 大小和格式/单位 | 范围/枚举 | 准确性/精度 | 其它特性 | 说明 |
|------|------|--------|----------|----------------|----------|------------|----------|------|
| 1 | 训练ID | training_id | CharField | 100字符 | - | - | 唯一 | 训练任务唯一标识ID |
| 2 | 状态 | status | CharField | 20字符 | pending, running, completed, failed, cancelled | - | - | 任务状态 |
| 3 | 算法名称 | algorithm_name | CharField | 50字符 | - | - | - | 算法名称 |
| 4 | 算法版本 | algorithm_version | CharField | 50字符 | - | - | - | 算法版本 |
| 5 | 强化学习类型 | rl_type | CharField | 20字符 | PPO, A3C, SAC, DDPG | - | - | 强化学习类型 |
| 6 | 当前回合 | current_episode | IntegerField | - | - | - | - | 当前训练回合数 |
| 7 | 总回合数 | total_episodes | IntegerField | - | - | - | - | 总回合数 |
| 8 | 预估时长 | estimated_duration | IntegerField | 秒 | - | - | 可空 | 预估训练时长 |
| 9 | 数据源 | data_source | CharField | 100字符 | - | - | - | 数据源 |
| 10 | 使用外部环境 | use_external_env | BooleanField | - | True, False | - | - | 是否使用外部环境 |
| 11 | 外部环境地址 | external_env_address | CharField | 255字符 | - | - | 可空 | 外部环境地址 |
| 12 | 采样时间 | sample_time | CharField | 20字符 | - | - | - | 采样时间 |
| 13 | 动作空间 | action_space | CharField | 50字符 | 离散动作空间, 连续动作空间 | - | - | 动作空间类型 |
| 14 | 观测空间 | observation_space | CharField | 50字符 | - | - | - | 观测空间类型 |
| 15 | 奖励函数 | reward_function | CharField | 50字符 | - | - | - | 奖励函数类型 |
| 16 | 学习率 | learning_rate | CharField | 20字符 | - | - | - | 学习率 |
| 17 | 训练轮数 | epochs | IntegerField | - | - | - | - | 训练轮数 |
| 18 | 批次大小 | batch_size | IntegerField | - | - | - | - | 批次大小 |
| 19 | 学习率策略 | learning_rate_strategy | CharField | 50字符 | - | - | - | 学习率策略 |
| 20 | 计算类型 | compute_type | CharField | 20字符 | - | - | - | 计算类型 |
| 21 | 验证比例 | validation_ratio | FloatField | - | 0.0-1.0 | - | - | 验证比例 |
| 22 | CPU数量 | cpu_count | CharField | 20字符 | - | - | - | CPU数量 |
| 23 | GPU数量 | gpu_count | CharField | 20字符 | - | - | - | GPU数量 |
| 24 | 保存路径 | save_path | CharField | 255字符 | - | - | - | 模型保存路径 |
| 25 | 保存名称 | save_name | CharField | 100字符 | - | - | - | 模型保存名称 |
| 26 | 创建者 | created_by | ForeignKey | - | - | - | - | 创建者用户 |
| 27 | 创建时间 | created_time | DateTimeField | - | - | - | 自动添加 | 创建时间 |
| 28 | 更新时间 | updated_time | DateTimeField | - | - | - | 自动更新 | 更新时间 |
| 29 | 开始时间 | started_time | DateTimeField | - | - | - | 可空 | 开始时间 |
| 30 | 完成时间 | completed_time | DateTimeField | - | - | - | 可空 | 完成时间 |

### 1.2 Dataset（数据集模型）

| 序号 | 名称 | 标识符 | 数据类型 | 大小和格式/单位 | 范围/枚举 | 准确性/精度 | 其它特性 | 说明 |
|------|------|--------|----------|----------------|----------|------------|----------|------|
| 1 | 数据集ID | dataset_id | CharField | 64字符 | - | - | 唯一 | 数据集唯一标识ID |
| 2 | 数据集名称 | name | CharField | 128字符 | - | - | - | 数据集名称 |
| 3 | 数据集描述 | description | TextField | - | - | - | 可空 | 数据集描述 |
| 4 | 数据集类型 | dataset_type | CharField | 20字符 | image, text, video, other | - | - | 数据集类型 |
| 5 | 数据集文件 | file | FileField | - | - | - | 可空 | 数据集文件路径 |
| 6 | 数据集存储地址 | address | CharField | 255字符 | - | - | 可空 | 数据集存储地址 |
| 7 | 原始文件路径 | original_file | CharField | 255字符 | - | - | 可空 | 原始文件路径 |
| 8 | 数据集大小 | size | CharField | 64字符 | - | - | 可空 | 数据集大小 |
| 9 | 样本数量 | sample_count | IntegerField | - | - | - | - | 样本数量 |
| 10 | 数据集状态 | dataset_status | CharField | 20字符 | enabled, disabled | - | - | 数据集状态 |
| 11 | 创建者 | creater | ForeignKey | - | - | - | - | 创建者用户 |
| 12 | 创建时间 | created_at | DateTimeField | - | - | - | 自动添加 | 创建时间 |
| 13 | 更新时间 | updated_at | DateTimeField | - | - | - | 自动更新 | 更新时间 |

### 1.3 Model（模型实例）

| 序号 | 名称 | 标识符 | 数据类型 | 大小和格式/单位 | 范围/枚举 | 准确性/精度 | 其它特性 | 说明 |
|------|------|--------|----------|----------------|----------|------------|----------|------|
| 1 | 模型ID | model_id | UUIDField | - | - | - | 唯一 | 模型唯一标识ID |
| 2 | 模型名称 | name | CharField | 255字符 | - | - | - | 模型名称 |
| 3 | 模型描述 | desc | CharField | 1024字符 | - | - | 可空 | 模型描述 |
| 4 | 模型文件名 | model_file | CharField | 1024字符 | - | - | 可空 | 模型文件名 |
| 5 | 模型配置文件名 | config_file | CharField | 1024字符 | - | - | 可空 | 模型配置文件名 |
| 6 | 模型大小 | size | CharField | 255字符 | - | - | 可空 | 模型大小 |
| 7 | 模型类型 | model_type | CharField | 50字符 | 目标检测车, 野战指挥车, 远程精式火箭炮, 无人机, 智能火控, 无人车, 未分类 | - | - | 模型类型 |
| 8 | 模型状态 | model_status | CharField | 50字符 | 训练中, 启用, 禁用, 完成, 中断, 未知 | - | - | 模型状态 |
| 9 | 创建者 | creater | ForeignKey | - | - | - | - | 创建者用户 |
| 10 | 创建时间 | create_time | DateTimeField | - | - | - | 自动添加 | 创建时间 |
| 11 | 模型创建时间 | model_create_time | DateTimeField | - | - | - | 可空 | 模型创建时间 |

### 1.4 Evaluation（评估管理模型）

| 序号 | 名称 | 标识符 | 数据类型 | 大小和格式/单位 | 范围/枚举 | 准确性/精度 | 其它特性 | 说明 |
|------|------|--------|----------|----------------|----------|------------|----------|------|
| 1 | 评估ID | evaluation_id | UUIDField | - | - | - | 唯一 | 评估唯一标识ID |
| 2 | 评估名称 | name | CharField | 255字符 | - | - | 唯一 | 评估名称 |
| 3 | 评估类型 | type | CharField | 50字符 | - | - | 可空 | 评估类型 |
| 4 | 评估描述 | description | CharField | 1024字符 | - | - | 可空 | 评估描述 |
| 5 | 评估代码 | code_file | FileField | - | - | - | 可空 | 评估代码文件路径 |
| 6 | 评估状态 | status | IntegerField | - | active, deprecated, developing | - | - | 评估状态 |
| 7 | 环境依赖 | environment | CharField | 1024字符 | - | - | 可空 | 环境依赖描述 |
| 8 | 仿真引擎 | simulation_engine | CharField | 255字符 | - | - | 可空 | 仿真引擎信息 |
| 9 | 策略规范 | policy_spec | TextField | - | - | - | 可空 | 策略规范说明 |
| 10 | GitLab项目ID | gitlab_project_id | IntegerField | - | - | - | 可空 | GitLab项目ID |
| 11 | GitLab项目URL | gitlab_project_url | URLField | 1024字符 | - | - | 可空 | GitLab项目URL |
| 12 | 创建时间 | create_time | DateTimeField | - | - | - | 自动添加 | 创建时间 |

### 1.5 RLTrainer（强化学习训练器）

| 序号 | 名称 | 标识符 | 数据类型 | 大小和格式/单位 | 范围/枚举 | 准确性/精度 | 其它特性 | 说明 |
|------|------|--------|----------|----------------|----------|------------|----------|------|
| 1 | 训练任务 | training_task | Object | - | - | - | - | 关联的训练任务对象 |
| 2 | 训练线程 | training_thread | Thread | - | - | - | 可空 | 训练线程对象 |
| 3 | 停止标志 | stop_flag | Boolean | - | True, False | - | - | 停止训练标志 |
| 4 | 当前回合 | current_episode | Integer | - | - | - | - | 当前训练回合数 |
| 5 | 指标历史 | metrics_history | List | - | - | - | - | 训练指标历史记录 |
| 6 | 资源历史 | resource_history | List | - | - | - | - | 资源使用历史记录 |

### 1.6 RLTrainingMetrics（强化学习训练指标模型）

| 属性名称 | 类型 | 默认值 | 用途 |
|---------|------|--------|------|
| task | ForeignKey | - | 关联的训练任务 |
| episode | IntegerField | - | 回合数 |
| cumulative_reward | FloatField | - | 累积奖励 |
| average_reward | FloatField | null | 平均奖励 |
| episode_length | IntegerField | null | 回合长度 |
| exploration_rate | FloatField | null | 探索率 |
| policy_loss | FloatField | null | 策略损失 |
| value_loss | FloatField | null | 价值损失 |
| entropy | FloatField | null | 熵 |
| timestamp | DateTimeField | auto_now_add | 时间戳 |

### 1.7 RLResourceMetrics（强化学习资源利用率指标模型）

| 属性名称 | 类型 | 默认值 | 用途 |
|---------|------|--------|------|
| task | ForeignKey | - | 关联的训练任务 |
| cpu_utilization | FloatField | - | CPU利用率 |
| npu_utilization | FloatField | 0.0 | NPU利用率 |
| memory_utilization | FloatField | - | 内存利用率 |
| network_io | FloatField | null | 网络IO |
| disk_io | FloatField | null | 磁盘IO |
| timestamp | DateTimeField | auto_now_add | 时间戳 |

### 1.8 RLTrainingConfig（强化学习训练配置模型）

| 属性名称 | 类型 | 默认值 | 用途 |
|---------|------|--------|------|
| config_id | CharField | uuid.uuid4 | 配置唯一标识ID |
| config_name | CharField | - | 配置名称 |
| description | TextField | '' | 配置描述 |
| algorithm_config | JSONField | - | 算法配置（JSON格式） |
| simulation_config | JSONField | - | 仿真配置（JSON格式） |
| agent_config | JSONField | - | 智能体配置（JSON格式） |
| hyper_params_config | JSONField | - | 超参数配置（JSON格式） |
| cluster_config | JSONField | - | 集群配置（JSON格式） |
| output_config | JSONField | - | 输出配置（JSON格式） |
| created_by | ForeignKey | - | 创建者用户 |
| created_time | DateTimeField | auto_now_add | 创建时间 |
| updated_time | DateTimeField | auto_now | 更新时间 |

### 1.9 Policy（策略管理模型）

| 属性名称 | 类型 | 默认值 | 用途 |
|---------|------|--------|------|
| policy_id | UUIDField | uuid.uuid4 | 策略唯一标识ID |
| name | CharField | - | 策略名称 |
| desc | CharField | null | 策略描述 |
| code_file | FileField | null | 策略代码文件 |
| status | IntegerField | 0 | 策略状态（待测试/测试中/可用/失败/已弃用） |
| policy_type | IntegerField | 0 | 策略类型（规则策略/强化学习策略/混合策略） |
| model | ForeignKey | null | 关联的算法模型 |
| features | JSONField | null | 特征标签（JSON格式） |
| evaluations | ManyToManyField | - | 关联的评估 |
| create_time | DateTimeField | auto_now_add | 创建时间 |
| update_time | DateTimeField | auto_now | 更新时间 |
| creater | ForeignKey | null | 创建用户 |

### 1.10 Environment（环境管理模型）

| 属性名称 | 类型 | 默认值 | 用途 |
|---------|------|--------|------|
| env_id | UUIDField | uuid.uuid4 | 环境唯一标识ID |
| name | CharField | - | 环境名称 |
| artifact_name | CharField | - | 镜像名称 |
| description | TextField | null | 环境描述 |
| version | CharField | - | 版本号 |
| status | IntegerField | 0 | 环境状态（可用/已弃用） |
| image_file | FileField | null | 环境镜像文件 |
| harbor_url | URLField | null | Harbor镜像地址 |
| gitlab_url | URLField | null | GitLab仓库地址 |
| env_variables | JSONField | null | 环境变量配置 |
| create_time | DateTimeField | auto_now_add | 创建时间 |
| update_time | DateTimeField | auto_now | 更新时间 |
| deprecated_time | DateTimeField | null | 弃用时间 |
| creator | ForeignKey | null | 创建者 |

### 1.11 Task（任务模型）

| 属性名称 | 类型 | 默认值 | 用途 |
|---------|------|--------|------|
| task_id | UUIDField | uuid.uuid4 | 任务唯一标识ID |
| name | CharField | - | 任务名称 |
| task_type | CharField | 'training' | 任务类型（训练/评估/推演） |
| create_time | DateTimeField | auto_now_add | 创建时间 |
| start_time | DateTimeField | null | 开始时间 |
| end_time | DateTimeField | null | 结束时间 |
| status | SmallIntegerField | 0 | 任务状态 |
| is_deleted | BooleanField | False | 是否删除标记 |
| deleted_time | DateTimeField | null | 删除时间 |
| owner | ForeignKey | null | 任务拥有者 |

### 1.12 Training（训练任务模型）

| 属性名称 | 类型 | 默认值 | 用途 |
|---------|------|--------|------|
| task | OneToOneField | - | 关联任务 |
| actor_num | IntegerField | 0 | actor数量 |
| learner_num | IntegerField | 0 | learner数量 |
| actor_per_cpu | FloatField | 0 | 单个actor CPU数量 |
| actor_per_gpu | FloatField | 0 | 单个actor GPU数量 |
| actor_per_memory | FloatField | 0 | 单个actor 内存数量 |
| learner_per_cpu | FloatField | 0 | 单个learner CPU数量 |
| learner_per_gpu | FloatField | 0 | 单个learner GPU数量 |
| learner_per_memory | FloatField | 0 | 单个learner 内存数量 |
| entrypoint | CharField | null | 入口函数 |
| mount_path | CharField | null | 挂载路径 |
| tb_url | CharField | null | Tensorboard地址 |
| model | ForeignKey | null | 关联模型 |
| algorithm | ForeignKey | null | 训练算法 |

## 2. 类操作表

### 2.1 RLTrainer（强化学习训练器）操作方法

| 序号 | 操作名称 | 项目唯一标识符 | 功能描述 |
|------|---------|----------------|----------|
| 1 | 初始化训练器 | `__init__` | 创建训练器实例，初始化基本属性 |
| 2 | 开始训练 | `start_training` | 启动训练线程，开始强化学习训练过程 |
| 3 | 停止训练 | `stop_training` | 停止当前正在运行的训练过程 |
| 4 | 执行训练主循环 | `_run_training` | 训练的主要逻辑，包含episode循环和指标记录 |
| 5 | 模拟训练回合 | `_simulate_episode` | 模拟一个强化学习episode的训练过程 |
| 6 | 选择动作 | `_select_action` | 根据策略选择下一步动作 |
| 7 | 计算奖励 | `_get_reward` | 根据动作和环境状态计算奖励值 |
| 8 | 更新策略 | `_update_policy` | 执行策略梯度更新 |
| 9 | 记录训练指标 | `_record_metrics` | 保存训练过程中的性能指标 |
| 10 | 记录资源使用 | `_record_resource_usage` | 记录CPU、GPU、内存等资源使用情况 |
| 11 | 获取训练进度 | `get_training_progress` | 返回当前训练进度信息 |
| 12 | 获取最新指标 | `get_latest_metrics` | 获取最近的训练指标数据 |
| 13 | 获取最新资源指标 | `get_latest_resource_metrics` | 获取最近的资源使用数据 |

### 2.2 RLTrainerManager（强化学习训练器管理器）操作方法

| 序号 | 操作名称 | 项目唯一标识符 | 功能描述 |
|------|---------|----------------|----------|
| 1 | 创建训练器 | `create_trainer` | 创建新的训练器实例并加入管理 |
| 2 | 获取训练器 | `get_trainer` | 根据训练ID获取训练器实例 |
| 3 | 移除训练器 | `remove_trainer` | 停止并移除指定的训练器实例 |
| 4 | 停止所有训练器 | `stop_all_trainers` | 停止并清理所有活跃的训练器 |

### 2.3 Dataset（数据集模型）操作方法

| 序号 | 操作名称 | 项目唯一标识符 | 功能描述 |
|------|---------|----------------|----------|
| 1 | 创建数据集 | `create` | 创建新的数据集实例 |
| 2 | 获取数据集 | `retrieve` | 获取单个数据集实例 |
| 3 | 列出数据集 | `list` | 列出所有数据集实例 |
| 4 | 更新数据集 | `update` | 更新数据集实例信息 |
| 5 | 删除数据集 | `destroy` | 删除数据集实例 |

### 2.4 Model（模型实例）操作方法

| 序号 | 操作名称 | 项目唯一标识符 | 功能描述 |
|------|---------|----------------|----------|
| 1 | 创建模型 | `create` | 创建新的模型实例 |
| 2 | 获取模型 | `retrieve` | 获取单个模型实例 |
| 3 | 列出模型 | `list` | 列出所有模型实例 |
| 4 | 更新模型 | `update` | 更新模型实例信息 |
| 5 | 删除模型 | `destroy` | 删除模型实例 |

### 2.5 Evaluation（评估管理模型）操作方法

| 序号 | 操作名称 | 项目唯一标识符 | 功能描述 |
|------|---------|----------------|----------|
| 1 | 创建评估 | `create` | 创建新的评估实例 |
| 2 | 获取评估 | `retrieve` | 获取单个评估实例 |
| 3 | 列出评估 | `list` | 列出所有评估实例 |
| 4 | 更新评估 | `update` | 更新评估实例信息 |
| 5 | 删除评估 | `destroy` | 删除评估实例 |

### 2.6 Django模型类通用操作方法

#### 实例方法（适用于RLTrainingTask、Policy、Environment、Task等模型类）

| 方法名称 | 功能 | 描述 | 参数 | 返回值 |
|---------|------|------|------|-------|
| `save` | 保存模型 | 将模型实例保存到数据库 | *args, **kwargs | None |
| `delete` | 删除模型 | 从数据库中删除模型实例 | 无 | tuple |
| `refresh_from_db` | 从数据库刷新 | 重新从数据库加载模型数据 | fields=None | None |
| `clean` | 数据验证 | 执行模型级别的数据验证 | 无 | None |
| `full_clean` | 完整验证 | 执行完整的数据验证流程 | exclude=None, validate_unique=True | None |
| `__str__` | 字符串表示 | 返回模型的字符串表示 | 无 | str |

#### 管理器方法（类级别方法）

| 方法名称 | 功能 | 描述 | 参数 | 返回值 |
|---------|------|------|------|-------|
| `objects.create` | 创建对象 | 创建并保存新的模型实例 | **kwargs | Model实例 |
| `objects.get` | 获取单个对象 | 根据条件获取单个模型实例 | **kwargs | Model实例 |
| `objects.filter` | 过滤查询 | 根据条件过滤查询结果 | **kwargs | QuerySet |
| `objects.all` | 获取所有对象 | 获取所有模型实例 | 无 | QuerySet |
| `objects.update` | 批量更新 | 批量更新模型实例 | **kwargs | int |
| `objects.delete` | 批量删除 | 批量删除模型实例 | 无 | tuple |
| `objects.count` | 统计数量 | 统计符合条件的对象数量 | 无 | int |
| `objects.exists` | 检查存在 | 检查是否存在符合条件的对象 | 无 | bool |

### 2.7 Environment（环境管理模型）特有方法

| 方法名称 | 功能 | 描述 | 参数 | 返回值 |
|---------|------|------|------|-------|
| `save` | 保存环境配置 | 保存环境配置，自动处理弃用时间 | *args, **kwargs | None |
| `__str__` | 字符串表示 | 返回环境名称和版本的字符串表示 | 无 | str |

### 2.8 工具函数

| 函数名称 | 功能 | 描述 | 参数 | 返回值 |
|---------|------|------|------|-------|
| `create_rl_training_task` | 创建RL训练任务 | 根据配置数据创建强化学习训练任务 | config_data: Dict, user | RLTrainingTask |

## 3. 类关系说明

### 3.1 继承关系
- 所有Django模型类（RLTrainingTask、Policy、Environment、Task、Training等）都继承自 `django.db.models.Model`
- RLTrainer 是独立的Python类，不继承Django模型

### 3.2 关联关系
- RLTrainingTask 与 RLTrainingMetrics：一对多关系
- RLTrainingTask 与 RLResourceMetrics：一对多关系  
- Policy 与 Model：多对一关系（可选）
- Policy 与 Evaluation：多对多关系（通过PolicyEvaluation中间表）
- Training 与 Task：一对一关系
- Training 与 Model：多对一关系（可选）
- Training 与 Algorithm：多对一关系（可选）

### 3.3 设计模式
- **工厂模式**：RLTrainerManager 用于创建和管理 RLTrainer 实例
- **单例模式**：RLTrainerManager 使用类级别的字典管理训练器实例
- **观察者模式**：训练过程中定期记录指标和资源使用情况
- **策略模式**：不同的强化学习算法（PPO、A3C、SAC、DDPG）可以使用不同的策略实现 