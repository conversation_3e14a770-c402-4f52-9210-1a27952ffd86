import json
import logging
import threading
import time
from datetime import datetime
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.http import StreamingHttpResponse

from backend_api.models.training import TrainingTask, DLTrainingConfig
from backend_api.serializers.training import (
    TrainingRequestSerializer,
    TrainingMetricsResponseSerializer,
    DLConfigSaveSerializer,
    DLConfigImportSerializer,
    DLTrainingConfigSerializer
)
from utils.yolov8_docker_trainer import YOLOv8DockerTrainer

logger = logging.getLogger(__name__)

# 存储活跃的训练任务
active_trainers = {}

def start_training_in_background(training_task, trainer):
    """在后台启动训练任务"""
    try:
        logger.info(f"在后台启动训练任务: {training_task.id}")
        success = trainer.start()
        
        if success:
            # 更新任务状态为运行中
            training_task.status = 'running'
            training_task.start_time = timezone.now()
            training_task.save()
            logger.info(f"训练任务 {training_task.id} 启动成功")
        else:
            # 启动失败，更新任务状态
            training_task.status = 'failed'
            training_task.save()
            logger.error(f"训练任务 {training_task.id} 启动失败")
            
    except Exception as e:
        logger.error(f"启动训练任务 {training_task.id} 时出现异常: {str(e)}")
        training_task.status = 'failed'
        training_task.save()

def stop_training_in_background(training_id, training_task, trainer):
    """在后台停止训练任务"""
    try:
        logger.info(f"在后台停止训练任务: {training_id}")
        success = trainer.stop()
        
        if success:
            # 停止成功，从活跃训练器列表中移除
            if training_id in active_trainers:
                del active_trainers[training_id]
            
            # 更新任务状态
            training_task.status = 'cancelled'
            training_task.end_time = timezone.now()
            training_task.save()
            logger.info(f"训练任务 {training_id} 取消成功")
        else:
            logger.warning(f"无法取消训练任务 {training_id}")
            
    except Exception as e:
        logger.error(f"停止训练任务 {training_id} 时出现异常: {str(e)}")
        # 尝试更新任务状态
        training_task.status = 'failed'
        training_task.save()

class TrainingStartView(APIView):
    """启动训练任务的视图"""
    
    def post(self, request):
        """启动训练任务"""
        serializer = TrainingRequestSerializer(data=request.data)
        if serializer.is_valid():
            try:
                # 创建训练任务记录
                training_task = serializer.save()
                training_task.status = 'pending'  # 将状态设置为等待中
                training_task.save()

                # 后续此次增加不同的训练任务调用不同的训练方法，目前只支持YOLOv8的训练
                
                # 初始化YOLOv8训练器
                trainer = YOLOv8DockerTrainer(training_task)
                active_trainers[training_task.id] = trainer
                
                # 在后台线程中启动训练
                thread = threading.Thread(
                    target=start_training_in_background,
                    args=(training_task, trainer)
                )
                thread.daemon = True  # 守护线程，主程序退出时会自动结束
                thread.start()
                
                # 立即返回响应
                return Response({
                    'success': True,
                    'message': '训练任务已提交',
                    'trainingId': training_task.id
                }, status=status.HTTP_202_ACCEPTED)
                
            except Exception as e:
                logger.error(f"提交训练失败: {str(e)}")
                # 发生异常时，确保更新任务状态
                if 'training_task' in locals():
                    training_task.status = 'failed'
                    training_task.save()
                return Response({
                    'success': False,
                    'message': f'提交训练失败: {str(e)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'success': False,
            'message': '请求参数无效',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

class TrainingMetricsView(APIView):
    """获取训练指标的视图"""
    
    def get(self, request, training_id):
        """获取训练指标"""
        try:
            training_task = get_object_or_404(TrainingTask, id=training_id)
            trainer = active_trainers.get(training_id)
            
            if trainer:
                # 使用新方法获取训练指标并保存到数据库
                trainer.save_training_metrics_to_db()
                
                # 更新训练任务状态
                task_status = trainer.get_task_status()
                if task_status and 'status' in task_status:
                    training_task.status = task_status['status']
                    training_task.save()
            
            # 使用响应序列化器格式化输出
            serializer = TrainingMetricsResponseSerializer(training_task)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"获取训练指标失败: {e}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TrainingCancelView(APIView):
    """取消训练任务的视图"""
    
    def post(self, request, training_id):
        """取消训练任务"""
        try:
            training_id = int(training_id)
            training_task = get_object_or_404(TrainingTask, id=training_id)
            logger.info(f"正在取消训练任务: {training_id}")
            
            trainer = active_trainers.get(training_id)
            if trainer:
                logger.info(f"找到活跃的训练器，任务ID: {training_id}")
                try:
                    # 在后台线程中停止训练
                    thread = threading.Thread(
                        target=stop_training_in_background,
                        args=(training_id, training_task, trainer)
                    )
                    thread.daemon = True  # 守护线程，主程序退出时会自动结束
                    thread.start()
                    
                    # 立即更新状态为"取消中"
                    training_task.status = 'cancelling'
                    training_task.save()
                    
                    # 立即返回响应
                    logger.info(f"训练任务 {training_id} 取消请求已提交")
                    return Response({
                        'success': True,
                        'message': '训练任务取消请求已提交'
                    }, status=status.HTTP_202_ACCEPTED)
                    
                except Exception as cancel_error:
                    logger.error(f"提交取消请求时发生错误: {cancel_error}")
                    return Response({
                        'success': False,
                        'message': f'取消训练请求提交失败: {str(cancel_error)}'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                logger.info(f"未找到活跃的训练器，任务ID: {training_id}")
                # 如果没有活跃的训练器，直接将任务状态设置为取消
                training_task.status = 'cancelled'
                training_task.end_time = timezone.now()
                training_task.save()
                return Response({
                    'success': True,
                    'message': '训练任务已取消（任务已结束或未启动）'
                })
        
        except ValueError:
            return Response({
                'success': False,
                'message': '无效的训练任务ID'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"取消视图中发生意外错误: {e}")
            return Response({
                'success': False,
                'message': f'服务器内部错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# 深度学习配置管理视图
class DLTrainingConfigSaveView(APIView):
    """保存深度学习训练配置"""
    
    parser_classes = [JSONParser]
    
    def post(self, request):
        try:
            logger.info(f"Saving DL config from user: {request.user.id}")
            logger.info(f"Request data: {request.data}")
            logger.info(f"Request content type: {request.content_type}")
            
            # 检查请求数据是否为空
            if not request.data:
                logger.warning("Empty request data received")
                return Response({
                    "success": False,
                    "error": {
                        "code": "EMPTY_DATA",
                        "message": "请求数据为空，请确保发送了有效的JSON数据"
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查配置名称字段（支持多种字段名）
            config_name = None
            for field_name in ['configName', 'config_name', 'name']:
                if field_name in request.data:
                    config_name = request.data[field_name]
                    break
            
            if not config_name:
                # 如果没有找到配置名称，尝试从算法配置中生成一个默认名称
                algorithm_config = request.data.get('algorithm', {})
                if algorithm_config:
                    default_name = f"DL配置_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    logger.info(f"No config name provided, using default: {default_name}")
                    request.data['configName'] = default_name
                else:
                    logger.warning(f"Missing configName in request data: {list(request.data.keys())}")
                    return Response({
                        "success": False,
                        "error": {
                            "code": "MISSING_CONFIG_NAME",
                            "message": "缺少必需字段 'configName'，或者请求数据格式不正确",
                            "details": {
                                "received_fields": list(request.data.keys()),
                                "expected_fields": ["configName", "algorithm", "training"],
                                "solutions": [
                                    "在请求JSON中添加 'configName' 字段",
                                    "确保Content-Type为application/json",
                                    "检查请求数据格式是否正确"
                                ],
                                "example": {
                                    "configName": "我的配置名称",
                                    "description": "配置描述",
                                    "algorithm": {"version": "v8", "modelPath": "/path/to/model"},
                                    "training": {"dataset": {"id": 1, "name": "dataset"}}
                                }
                            }
                        }
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                # 统一使用 configName 字段
                request.data['configName'] = config_name
            
            serializer = DLConfigSaveSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"Invalid config save request: {serializer.errors}")
                return Response({
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": "请求参数验证失败",
                        "details": serializer.errors,
                        "help": {
                            "configName": "配置名称（必需字段）",
                            "description": "配置描述（可选）",
                            "algorithm": "算法配置（必需）",
                            "training": "训练配置（必需）",
                            "resources": "资源配置（可选）",
                            "parameters": "参数配置（可选）",
                            "otherParams": "其他参数配置（可选）"
                        }
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            data = serializer.validated_data
            
            # 检查配置名称是否已存在
            if DLTrainingConfig.objects.filter(
                config_name=data['configName'],
                created_by=request.user
            ).exists():
                return Response({
                    "success": False,
                    "error": {
                        "code": "CONFIG_EXISTS",
                        "message": "配置名称已存在，请使用其他名称"
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 创建配置记录
            config = DLTrainingConfig.objects.create(
                config_name=data['configName'],
                description=data.get('description', ''),
                algorithm_config=data['algorithm'],
                training_config=data['training'],
                resources_config=data.get('resources', {}),
                parameters_config=data.get('parameters', {}),
                other_params_config=data.get('otherParams', {}),
                created_by=request.user
            )
            
            logger.info(f"Successfully saved config: {config.config_id}")
            return Response({
                "success": True,
                "data": {
                    "configId": config.config_id,
                    "configName": config.config_name,
                    "message": "配置保存成功"
                }
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Failed to save DL config: {e}", exc_info=True)
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DLTrainingConfigImportView(APIView):
    """批量导入深度学习配置"""
    
    parser_classes = [MultiPartParser]
    
    def post(self, request):
        try:
            logger.info(f"Importing DL config from user: {request.user.id}")
            
            serializer = DLConfigImportSerializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"Invalid config import request: {serializer.errors}")
                return Response({
                    "success": False,
                    "error": {
                        "code": "VALIDATION_ERROR",
                        "message": "请求参数验证失败",
                        "details": serializer.errors
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            config_file = serializer.validated_data['config']
            
            try:
                # 读取并解析JSON配置
                config_content = config_file.read().decode('utf-8')
                config_data = json.loads(config_content)
                
                # 验证配置格式
                required_fields = ['configName', 'algorithm', 'training']
                for field in required_fields:
                    if field not in config_data:
                        return Response({
                            "success": False,
                            "error": {
                                "code": "INVALID_CONFIG",
                                "message": f"配置文件缺少必需字段: {field}"
                            }
                        }, status=status.HTTP_400_BAD_REQUEST)
                
                # 检查配置名称是否已存在
                if DLTrainingConfig.objects.filter(
                    config_name=config_data['configName'],
                    created_by=request.user
                ).exists():
                    return Response({
                        "success": False,
                        "error": {
                            "code": "CONFIG_EXISTS",
                            "message": "配置名称已存在，请修改配置名称后重新导入"
                        }
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # 创建配置记录
                config = DLTrainingConfig.objects.create(
                    config_name=config_data['configName'],
                    description=config_data.get('description', ''),
                    algorithm_config=config_data['algorithm'],
                    training_config=config_data['training'],
                    resources_config=config_data.get('resources', {}),
                    parameters_config=config_data.get('parameters', {}),
                    other_params_config=config_data.get('otherParams', {}),
                    created_by=request.user
                )
                
                logger.info(f"Successfully imported config: {config.config_id}")
                return Response({
                    "success": True,
                    "data": {
                        "configId": config.config_id,
                        "configName": config.config_name,
                        "message": "配置导入成功"
                    }
                }, status=status.HTTP_201_CREATED)
                
            except json.JSONDecodeError:
                return Response({
                    "success": False,
                    "error": {
                        "code": "INVALID_JSON",
                        "message": "配置文件格式错误，请确保是有效的JSON格式"
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            except UnicodeDecodeError:
                return Response({
                    "success": False,
                    "error": {
                        "code": "INVALID_ENCODING",
                        "message": "配置文件编码错误，请使用UTF-8编码"
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            logger.error(f"Failed to import DL config: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DLTrainingConfigListView(APIView):
    """获取深度学习配置列表"""
    
    def get(self, request):
        try:
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('pageSize', 20))
            search = request.GET.get('search', '')
            
            # 构建查询
            queryset = DLTrainingConfig.objects.filter(created_by=request.user)
            
            if search:
                queryset = queryset.filter(
                    Q(config_name__icontains=search) |
                    Q(description__icontains=search)
                )
            
            # 分页
            paginator = Paginator(queryset.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = DLTrainingConfigSerializer(page_obj.object_list, many=True)
            
            return Response({
                "success": True,
                "data": serializer.data,
                "total": paginator.count,
                "page": page,
                "pageSize": page_size,
                "totalPages": paginator.num_pages
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get DL config list: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DLTrainingConfigDeleteView(APIView):
    """删除深度学习训练配置"""
    
    def delete(self, request, config_id):
        try:
            # 查找配置
            try:
                config = DLTrainingConfig.objects.get(
                    config_id=config_id,
                    created_by=request.user
                )
            except DLTrainingConfig.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "CONFIG_NOT_FOUND",
                        "message": "配置不存在或您没有权限删除"
                    }
                }, status=status.HTTP_404_NOT_FOUND)
            
            config_name = config.config_name
            config.delete()
            
            logger.info(f"Successfully deleted DL config: {config_id}")
            return Response({
                "success": True,
                "message": f"配置 '{config_name}' 已删除"
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to delete DL config: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DLTrainingConfigExportView(APIView):
    """导出深度学习训练配置"""
    
    def get(self, request, config_id):
        try:
            # 查找配置
            try:
                config = DLTrainingConfig.objects.get(
                    config_id=config_id,
                    created_by=request.user
                )
            except DLTrainingConfig.DoesNotExist:
                return Response({
                    "success": False,
                    "error": {
                        "code": "CONFIG_NOT_FOUND",
                        "message": "配置不存在或您没有权限导出"
                    }
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 构建导出数据
            export_data = {
                "configName": config.config_name,
                "description": config.description,
                "algorithm": config.algorithm_config,
                "training": config.training_config,
                "resources": config.resources_config,
                "parameters": config.parameters_config,
                "otherParams": config.other_params_config,
                "exportTime": timezone.now().isoformat(),
                "version": "1.0"
            }
            
            from django.http import JsonResponse
            response = JsonResponse(export_data, json_dumps_params={'ensure_ascii': False, 'indent': 2})
            response['Content-Disposition'] = f'attachment; filename="{config.config_name}_config.json"'
            return response
            
        except Exception as e:
            logger.error(f"Failed to export DL config: {e}")
            return Response({
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": f"内部错误: {str(e)}"
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingLogStreamView(APIView):
    """实时推送训练过程中的后端日志信息 (Server-Sent Events)"""

    def get(self, request, training_id):
        """
        建立SSE连接，实时推送后端请求日志信息
        URL: /api/training/{training_id}/logs/
        """
        try:
            training_id = int(training_id)
            training_task = get_object_or_404(TrainingTask, id=training_id)

            def event_stream():
                """生成SSE事件流"""
                try:
                    # 发送连接建立消息
                    yield f"data: {json.dumps({'type': 'connected', 'message': '连接已建立', 'timestamp': time.time()})}\n\n"

                    # 持续推送训练进度
                    last_metrics = None
                    connection_start = time.time()

                    while True:
                        try:
                            # 检查连接是否超时 (30分钟)
                            if time.time() - connection_start > 1800:
                                yield f"data: {json.dumps({'type': 'timeout', 'message': '连接超时', 'timestamp': time.time()})}\n\n"
                                break

                            # 刷新任务状态
                            training_task.refresh_from_db()

                            # 获取训练器实例
                            trainer = active_trainers.get(training_id)

                            if trainer:
                                # 获取最新的训练指标
                                try:
                                    current_metrics = trainer.get_training_metrics()
                                    task_status = trainer.get_task_status()

                                    # 构建进度数据
                                    progress_data = {
                                        'type': 'progress',
                                        'training_id': training_id,
                                        'status': training_task.status,
                                        'timestamp': time.time(),
                                        'metrics': current_metrics,
                                        'task_status': task_status
                                    }

                                    # 只有当指标发生变化时才推送
                                    if current_metrics != last_metrics:
                                        yield f"data: {json.dumps(progress_data)}\n\n"
                                        last_metrics = current_metrics

                                    # 如果训练完成或失败，发送最终状态并结束
                                    if training_task.status in ['completed', 'failed', 'cancelled']:
                                        final_data = {
                                            'type': 'finished',
                                            'training_id': training_id,
                                            'status': training_task.status,
                                            'message': f'训练已{training_task.status}',
                                            'timestamp': time.time(),
                                            'final_metrics': current_metrics
                                        }
                                        yield f"data: {json.dumps(final_data)}\n\n"
                                        break

                                except Exception as metrics_error:
                                    logger.warning(f"获取训练指标失败: {metrics_error}")
                                    error_data = {
                                        'type': 'error',
                                        'message': f'获取指标失败: {str(metrics_error)}',
                                        'timestamp': time.time()
                                    }
                                    yield f"data: {json.dumps(error_data)}\n\n"
                            else:
                                # 没有活跃的训练器
                                if training_task.status in ['completed', 'failed', 'cancelled']:
                                    final_data = {
                                        'type': 'finished',
                                        'training_id': training_id,
                                        'status': training_task.status,
                                        'message': f'训练已{training_task.status}',
                                        'timestamp': time.time()
                                    }
                                    yield f"data: {json.dumps(final_data)}\n\n"
                                    break
                                else:
                                    # 训练器不存在但任务状态未完成
                                    status_data = {
                                        'type': 'status',
                                        'training_id': training_id,
                                        'status': training_task.status,
                                        'message': '等待训练启动...',
                                        'timestamp': time.time()
                                    }
                                    yield f"data: {json.dumps(status_data)}\n\n"

                            # 每2秒检查一次
                            time.sleep(2)

                        except Exception as loop_error:
                            logger.error(f"训练进度推送循环错误: {loop_error}")
                            error_data = {
                                'type': 'error',
                                'message': f'推送错误: {str(loop_error)}',
                                'timestamp': time.time()
                            }
                            yield f"data: {json.dumps(error_data)}\n\n"
                            time.sleep(5)  # 错误时等待更长时间

                except Exception as stream_error:
                    logger.error(f"事件流生成错误: {stream_error}")
                    yield f"data: {json.dumps({'type': 'error', 'message': f'流错误: {str(stream_error)}', 'timestamp': time.time()})}\n\n"

            # 创建SSE响应
            response = StreamingHttpResponse(
                event_stream(),
                content_type='text/event-stream'
            )
            response['Cache-Control'] = 'no-cache'
            response['Connection'] = 'keep-alive'
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Headers'] = 'Cache-Control'

            return response

        except ValueError:
            return Response({
                'success': False,
                'message': '无效的训练任务ID'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"建立训练进度流失败: {e}")
            return Response({
                'success': False,
                'message': f'建立连接失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingLogsView(APIView):
    """获取训练日志的视图"""

    def get(self, request, training_id):
        """
        获取训练日志
        参数:
            lines: 获取的行数 (默认: 100)
            mode: 获取模式 ('tail': 最后几行, 'head': 前几行, 'all': 全部)
            search: 搜索关键词 (可选)
        """
        try:
            training_id = int(training_id)
            training_task = get_object_or_404(TrainingTask, id=training_id)

            # 获取参数
            lines = int(request.query_params.get('lines', 100))
            mode = request.query_params.get('mode', 'tail')
            search_pattern = request.query_params.get('search', '')

            # 获取训练器实例
            trainer = active_trainers.get(training_id)
            if not trainer:
                return Response({
                    'success': False,
                    'message': '训练任务未找到或已结束'
                }, status=status.HTTP_404_NOT_FOUND)

            # 根据是否有搜索关键词选择不同的方法
            if search_pattern:
                logs = trainer.search_logs(search_pattern, lines if mode != 'all' else None)
                log_type = 'search'
            else:
                logs = trainer.get_training_logs(lines, mode)
                log_type = mode

            # 获取日志文件信息
            log_info = trainer.get_log_file_info()

            return Response({
                'success': True,
                'data': {
                    'training_id': training_id,
                    'logs': logs,
                    'log_type': log_type,
                    'lines_requested': lines,
                    'search_pattern': search_pattern,
                    'log_info': log_info,
                    'timestamp': time.time()
                }
            }, status=status.HTTP_200_OK)

        except ValueError:
            return Response({
                'success': False,
                'message': '无效的训练任务ID'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"获取训练日志失败: {e}")
            return Response({
                'success': False,
                'message': f'获取日志失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingLogStreamView(APIView):
    """实时推送训练日志的视图 (Server-Sent Events)"""

    def get(self, request, training_id):
        """
        建立SSE连接，实时推送训练日志
        URL: /backend/training/{training_id}/log-stream/
        """
        try:
            training_id = int(training_id)
            training_task = get_object_or_404(TrainingTask, id=training_id)

            def log_event_stream():
                """生成日志事件流"""
                try:
                    # 发送连接建立消息
                    yield f"data: {json.dumps({'type': 'connected', 'message': '日志流连接已建立', 'timestamp': time.time()})}\n\n"

                    # 获取训练器实例
                    trainer = active_trainers.get(training_id)
                    if not trainer:
                        yield f"data: {json.dumps({'type': 'error', 'message': '训练任务未找到', 'timestamp': time.time()})}\n\n"
                        return

                    # 首先发送最近的日志
                    initial_logs = trainer.get_training_logs(lines=20, mode='tail')
                    if initial_logs and initial_logs != "暂无日志":
                        yield f"data: {json.dumps({'type': 'initial', 'logs': initial_logs, 'timestamp': time.time()})}\n\n"

                    # 开始实时跟踪日志
                    connection_start = time.time()
                    last_log_check = time.time()

                    while True:
                        try:
                            # 检查连接是否超时 (30分钟)
                            if time.time() - connection_start > 1800:
                                yield f"data: {json.dumps({'type': 'timeout', 'message': '连接超时', 'timestamp': time.time()})}\n\n"
                                break

                            # 刷新任务状态
                            training_task.refresh_from_db()

                            # 如果训练已结束，发送最终日志并断开连接
                            if training_task.status in ['completed', 'failed', 'cancelled']:
                                final_logs = trainer.get_training_logs(lines=50, mode='tail')
                                yield f"data: {json.dumps({'type': 'final', 'status': training_task.status, 'logs': final_logs, 'timestamp': time.time()})}\n\n"
                                break

                            # 每5秒检查一次新日志
                            current_time = time.time()
                            if current_time - last_log_check >= 5:
                                # 获取最新的日志行
                                recent_logs = trainer.get_training_logs(lines=10, mode='tail')
                                if recent_logs and recent_logs != "暂无日志":
                                    yield f"data: {json.dumps({'type': 'update', 'logs': recent_logs, 'timestamp': current_time})}\n\n"

                                last_log_check = current_time

                            time.sleep(2)  # 每2秒检查一次状态

                        except Exception as loop_error:
                            logger.error(f"日志流循环错误: {loop_error}")
                            yield f"data: {json.dumps({'type': 'error', 'message': f'日志流错误: {str(loop_error)}', 'timestamp': time.time()})}\n\n"
                            time.sleep(5)

                except Exception as stream_error:
                    logger.error(f"日志事件流生成错误: {stream_error}")
                    yield f"data: {json.dumps({'type': 'error', 'message': f'流错误: {str(stream_error)}', 'timestamp': time.time()})}\n\n"

            # 创建SSE响应
            response = StreamingHttpResponse(
                log_event_stream(),
                content_type='text/event-stream'
            )
            response['Cache-Control'] = 'no-cache'
            response['Connection'] = 'keep-alive'
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Headers'] = 'Cache-Control'

            return response

        except ValueError:
            return Response({
                'success': False,
                'message': '无效的训练任务ID'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"建立日志流失败: {e}")
            return Response({
                'success': False,
                'message': f'建立连接失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)