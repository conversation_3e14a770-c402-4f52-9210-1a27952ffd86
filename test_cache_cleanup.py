#!/usr/bin/env python3
"""
测试YOLOv8DockerTrainer缓存清理功能的脚本
"""

import sys
import os
import logging
from unittest.mock import Mock, MagicMock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cache_cleanup():
    """测试缓存清理功能"""
    
    # 模拟训练任务对象
    mock_training_task = Mock()
    mock_training_task.dataset_name = "test_dataset"
    mock_training_task.epochs = 100
    mock_training_task.cpu_count = 4
    mock_training_task.npu_count = 1
    mock_training_task.model_path = "yolov8n.pt"
    
    # 模拟SSH客户端
    mock_ssh_client = Mock()
    
    # 模拟SSH命令执行结果
    def mock_exec_command(command):
        stdout_mock = Mock()
        stderr_mock = Mock()
        
        if "test -d" in command and "labels" in command:
            # 模拟labels目录存在
            stdout_mock.read.return_value.decode.return_value.strip.return_value = "exists"
        elif "find" in command and "*.cache" in command and "-delete" in command:
            # 模拟删除缓存文件成功
            stderr_mock.read.return_value.decode.return_value.strip.return_value = ""
        elif "find" in command and "*.cache" in command and "wc -l" in command:
            # 模拟验证清理结果
            stdout_mock.read.return_value.decode.return_value.strip.return_value = "0"
        else:
            stdout_mock.read.return_value.decode.return_value.strip.return_value = ""
            stderr_mock.read.return_value.decode.return_value.strip.return_value = ""
            
        return None, stdout_mock, stderr_mock
    
    mock_ssh_client.exec_command = mock_exec_command
    
    # 模拟资源管理器
    mock_resource_manager = Mock()
    mock_resource_manager.query_task.return_value = {
        'records': [{
            'calculateDeviceId': 'test_device_id'
        }]
    }
    mock_resource_manager.query_bucket_list.return_value = [{
        'mountPath': '/root/siton-data-test123'
    }]
    
    try:
        # 导入YOLOv8DockerTrainer
        from utils.yolov8_docker_trainer import YOLOv8DockerTrainer
        
        # 创建训练器实例
        trainer = YOLOv8DockerTrainer(mock_training_task)
        trainer.ssh_client = mock_ssh_client
        trainer.resource_manager = mock_resource_manager
        trainer.task_info = {
            'taskName': 'test_task',
            'taskId': 'test_id'
        }
        
        # 测试缓存清理功能
        logger.info("开始测试缓存清理功能...")
        result = trainer._cleanup_dataset_cache()
        
        if result:
            logger.info("✅ 缓存清理功能测试通过")
        else:
            logger.error("❌ 缓存清理功能测试失败")
            
        # 验证SSH命令是否被正确调用
        call_count = mock_ssh_client.exec_command.call_count
        logger.info(f"SSH命令调用次数: {call_count}")
        
        # 打印所有调用的命令
        for i, call in enumerate(mock_ssh_client.exec_command.call_args_list):
            command = call[0][0]  # 获取第一个参数（命令）
            logger.info(f"命令 {i+1}: {command}")
            
        return result
        
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        return False
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        return False

def test_training_scenarios():
    """测试不同训练场景下的缓存清理"""
    
    logger.info("\n" + "="*50)
    logger.info("测试不同训练场景下的缓存清理")
    logger.info("="*50)
    
    scenarios = [
        ("训练成功完成", "completed"),
        ("训练失败", "failed"),
        ("训练被手动停止", "stopped")
    ]
    
    for scenario_name, scenario_type in scenarios:
        logger.info(f"\n测试场景: {scenario_name}")
        logger.info("-" * 30)
        
        # 这里可以添加具体的场景测试逻辑
        # 由于涉及到复杂的模拟，这里只是展示框架
        logger.info(f"场景 '{scenario_name}' 应该在 {scenario_type} 状态下清理缓存文件")

if __name__ == "__main__":
    logger.info("开始测试YOLOv8DockerTrainer缓存清理功能")
    
    # 测试基本的缓存清理功能
    success = test_cache_cleanup()
    
    # 测试不同训练场景
    test_training_scenarios()
    
    if success:
        logger.info("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        logger.error("\n💥 测试失败！")
        sys.exit(1)
