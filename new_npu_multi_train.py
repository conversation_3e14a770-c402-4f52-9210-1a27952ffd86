#!/usr/bin/env python3
"""
华为NPU多机多卡YOLO训练脚本
支持单机多卡和多机多卡训练
"""

import sys
import os
import argparse
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
ultralytics_dir = current_dir / "ultralytics_v8"
sys.path.insert(0, str(ultralytics_dir))

# 设置环境变量
os.environ["PYTHONPATH"] = str(ultralytics_dir)

def setup_npu_environment():
    """设置NPU训练环境"""
    try:
        import torch_npu
        print(f"✅ torch_npu版本: {torch_npu.__version__}")
        
        # 检查NPU可用性
        if not torch_npu.npu.is_available():
            raise RuntimeError("NPU不可用，请检查驱动和torch_npu安装")
        
        npu_count = torch_npu.npu.device_count()
        print(f"✅ 检测到 {npu_count} 个NPU设备")
        
        # 显示NPU信息
        for i in range(npu_count):
            device_name = torch_npu.npu.get_device_name(i)
            device_props = torch_npu.npu.get_device_properties(i)
            memory_gb = device_props.total_memory / 1024**3
            print(f"   NPU:{i} - {device_name} ({memory_gb:.1f}GB)")
        
        return npu_count
        
    except ImportError:
        raise ImportError("torch_npu未安装，请先安装华为NPU工具包")
    except Exception as e:
        raise RuntimeError(f"NPU环境检查失败: {e}")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='华为NPU多机多卡YOLO训练')
    
    # 基本训练参数
    parser.add_argument('--model', type=str, default='yolo11n.pt', help='模型文件路径')
    parser.add_argument('--data', type=str, default='coco8.yaml', help='数据集配置文件')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮次')
    parser.add_argument('--batch', type=int, default=16, help='批次大小')
    parser.add_argument('--imgsz', type=int, default=640, help='图像尺寸')
    parser.add_argument('--lr0', type=float, default=0.01, help='初始学习率')
    
    # 设备配置
    parser.add_argument('--device', type=str, default='auto', 
                       help='设备配置: auto(自动), npu:0(单NPU), npu:0,1(多NPU), npu:0,1,2,3(4NPU)')
    
    # 分布式训练参数
    parser.add_argument('--multi-machine', action='store_true', help='启用多机训练')
    parser.add_argument('--master-addr', type=str, default='127.0.0.1', help='主节点IP地址')
    parser.add_argument('--master-port', type=str, default='29500', help='主节点端口')
    parser.add_argument('--world-size', type=int, default=1, help='总进程数(节点数*每节点NPU数)')
    parser.add_argument('--rank', type=int, default=0, help='当前节点在所有节点中的排名')
    
    # 保存配置
    parser.add_argument('--project', type=str, default='runs/train', help='项目目录')
    parser.add_argument('--name', type=str, default='npu_train', help='实验名称')
    parser.add_argument('--exist-ok', action='store_true', help='允许覆盖现有结果')
    
    # 性能优化
    parser.add_argument('--amp', action='store_true', help='启用自动混合精度训练')
    parser.add_argument('--workers', type=int, default=8, help='数据加载线程数')
    parser.add_argument('--cache', action='store_true', help='启用数据缓存')
    
    return parser.parse_args()

def get_device_config(device_arg, npu_count):
    """根据参数获取设备配置"""
    if device_arg == 'auto':
        if npu_count > 1:
            device = f"npu:{','.join(map(str, range(npu_count)))}"
            print(f"🔄 自动配置: 使用所有{npu_count}个NPU - {device}")
        else:
            device = "npu:0"
            print("🔄 自动配置: 使用单个NPU - npu:0")
    else:
        device = device_arg
        print(f"🔧 手动配置: {device}")
    
    return device

def single_machine_train(args, device):
    """单机训练（单卡或多卡）"""
    from ultralytics import YOLO
    
    print("=" * 60)
    print("🚀 启动单机NPU训练")
    print(f"📱 设备: {device}")
    print(f"📊 批次大小: {args.batch}")
    print(f"🔄 训练轮次: {args.epochs}")
    print("=" * 60)
    
    # 创建模型
    model = YOLO(args.model)
    
    # 训练配置
    train_args = {
        'data': args.data,
        'epochs': args.epochs,
        'batch': args.batch,
        'imgsz': args.imgsz,
        'device': device,
        'lr0': args.lr0,
        'project': args.project,
        'name': args.name,
        'exist_ok': args.exist_ok,
        'amp': args.amp,
        'workers': args.workers,
        'cache': args.cache,
        'verbose': True,
        'save': True,
        'plots': True,
        'val': True,
    }
    
    print("🎯 开始训练...")
    results = model.train(**train_args)
    
    print("✅ 训练完成！")
    print(f"📈 结果保存在: {model.trainer.save_dir}")
    return results

def multi_machine_train_setup(args, device):
    """多机训练设置"""
    print("=" * 60)
    print("🌐 配置多机多卡训练")
    print(f"🏠 主节点: {args.master_addr}:{args.master_port}")
    print(f"🔢 总进程数: {args.world_size}")
    print(f"📍 当前节点排名: {args.rank}")
    print(f"📱 设备: {device}")
    print("=" * 60)
    
    # 设置分布式环境变量
    os.environ['MASTER_ADDR'] = args.master_addr
    os.environ['MASTER_PORT'] = args.master_port
    os.environ['WORLD_SIZE'] = str(args.world_size)
    os.environ['RANK'] = str(args.rank)
    
    # 如果是多NPU设备，设置LOCAL_RANK
    if ',' in device:
        npu_list = device.replace('npu:', '').split(',')
        os.environ['LOCAL_RANK'] = '0'  # 主进程
    else:
        os.environ['LOCAL_RANK'] = '0'
    
    print("🔧 环境变量设置完成:")
    print(f"   MASTER_ADDR = {os.environ['MASTER_ADDR']}")
    print(f"   MASTER_PORT = {os.environ['MASTER_PORT']}")
    print(f"   WORLD_SIZE = {os.environ['WORLD_SIZE']}")
    print(f"   RANK = {os.environ['RANK']}")
    print(f"   LOCAL_RANK = {os.environ['LOCAL_RANK']}")
    
    return single_machine_train(args, device)

def print_training_guide():
    """打印训练指南"""
    print("\n" + "=" * 80)
    print("📖 华为NPU多机多卡训练指南")
    print("=" * 80)
    
    print("\n🔹 单机单卡训练:")
    print("   python npu_multi_train.py --device npu:0")
    
    print("\n🔹 单机多卡训练:")
    print("   python npu_multi_train.py --device npu:0,1,2,3")
    
    print("\n🔹 多机多卡训练:")
    print("   # 主节点 (rank=0):")
    print("   python npu_multi_train.py --multi-machine --master-addr ************* \\")
    print("          --world-size 8 --rank 0 --device npu:0,1,2,3")
    print("   ")
    print("   # 从节点 (rank=1):")  
    print("   python npu_multi_train.py --multi-machine --master-addr ************* \\")
    print("          --world-size 8 --rank 1 --device npu:0,1,2,3")
    
    print("\n🔹 使用torch.distributed.run (推荐):")
    print("   # 单机4卡:")
    print("   python -m torch.distributed.run --nproc_per_node=4 \\")
    print("          npu_multi_train.py --device npu:0,1,2,3")
    print("   ")
    print("   # 多机多卡 (2台机器，每台4卡):")
    print("   # 机器0:")
    print("   python -m torch.distributed.run --nproc_per_node=4 --nnodes=2 \\")
    print("          --node_rank=0 --master_addr=************* --master_port=29500 \\")
    print("          npu_multi_train.py --device npu:0,1,2,3")
    print("   # 机器1:")
    print("   python -m torch.distributed.run --nproc_per_node=4 --nnodes=2 \\")
    print("          --node_rank=1 --master_addr=************* --master_port=29500 \\")
    print("          npu_multi_train.py --device npu:0,1,2,3")
    
    print("\n💡 性能优化建议:")
    print("   - 使用 --amp 启用混合精度训练")
    print("   - 调整 --batch 大小以充分利用NPU内存")
    print("   - 使用 --cache 缓存数据集以提高训练速度")
    print("   - 确保网络带宽足够支持多机通信")
    
    print("\n⚠️  注意事项:")
    print("   - 确保所有机器能互相访问主节点IP")
    print("   - 所有机器的代码和数据集路径要一致")
    print("   - 批次大小必须能被NPU总数整除")
    print("   - 多机训练时只在主节点(rank=0)显示输出")

def main():
    """主函数"""
    args = parse_arguments()
    
    print("🚀 华为NPU多机多卡YOLO训练")
    print(f"📁 工作目录: {current_dir}")
    
    try:
        # 检查NPU环境
        npu_count = setup_npu_environment()
        
        # 配置设备
        device = get_device_config(args.device, npu_count)
        
        # 检查batch size是否合理
        if ',' in device:
            device_count = len(device.replace('npu:', '').split(','))
            if args.batch % device_count != 0:
                print(f"⚠️  警告: batch={args.batch} 不能被NPU数量{device_count}整除")
                suggested_batch = (args.batch // device_count + 1) * device_count
                print(f"💡 建议使用 batch={suggested_batch}")
                
                response = input("是否使用建议的batch size? (y/n): ")
                if response.lower() == 'y':
                    args.batch = suggested_batch
                    print(f"✅ 已调整batch size为: {args.batch}")
        
        # 选择训练模式
        if args.multi_machine:
            results = multi_machine_train_setup(args, device)
        else:
            results = single_machine_train(args, device)
            
        print("\n✅ 所有训练任务完成！")
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        print_training_guide()
        sys.exit(1)

if __name__ == "__main__":
    main() 