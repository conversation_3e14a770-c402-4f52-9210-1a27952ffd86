from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from rest_framework.status import HTTP_201_CREATED, HTTP_200_OK, HTTP_400_BAD_REQUEST
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

from rest_framework.permissions import IsAdminUser, AllowAny, IsAuthenticated, BasePermission
from rest_framework.decorators import action
from rest_framework.filters import OrderingFilter, SearchFilter
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.pagination import PageNumberPagination
from django_filters import rest_framework as filters
import logging


from django.db.utils import IntegrityError
from django.utils import timezone
from user.models.user import User
from user.serializers.token import MyTokenObtainPairSerializer
from user.serializers.user import UserSerializer, UserRegistSerializer, UserUpdateSerializer
from utils import k8s_client
from utils.certificate_manager import certificate_manager
import logging
import datetime

logger = logging.getLogger(__name__)


# 自定义权限类：允许管理员访问所有用户，普通用户只能访问自己的账号
class IsSelfOrAdmin(BasePermission):
    """
    允许用户访问自己的账号信息，管理员可以访问所有账号
    """
    def has_permission(self, request, view):
        # 允许已登录用户访问列表视图，但在方法中会进行过滤
        return request.user.is_authenticated
        
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问任何账号
        if request.user.is_staff or request.user.is_superuser:
            return True
            
        # 普通用户只能访问自己的账号
        return obj.id == request.user.id


class UserFilter(filters.FilterSet):
    """用户过滤器 - 只支持真实姓名、电话和状态搜索"""
    real_name = filters.CharFilter(lookup_expr='icontains', help_text='真实姓名，支持模糊搜索')
    phone = filters.CharFilter(lookup_expr='icontains', help_text='电话号码，支持模糊搜索')
    is_active = filters.BooleanFilter(help_text='用户状态')
    
    class Meta:
        model = User
        fields = ['real_name', 'phone', 'is_active']

class CustomPagination(PageNumberPagination):
    """自定义分页器"""
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class UserViewSets(ModelViewSet):
    """
    用户资源管理视图集
    
    提供以下功能：
    - 用户注册
    - 获取用户信息
    - 更新用户信息
    - 删除用户账户
    - 激活/停用用户账户
    
    权限控制：
    - 管理员可以访问所有用户的信息
    - 普通用户只能访问和修改自己的账号信息
    - 注册接口对所有人开放
    
    支持：
    - 分页：使用page和page_size参数
    - 排序：使用ordering参数，如：ordering=-date_joined
    - 过滤：
        - real_name: 真实姓名（模糊搜索）
        - phone: 电话号码（模糊搜索）
        - is_active: 用户状态
    - 搜索：支持按real_name、phone搜索
    """
    queryset = User.objects.all()
    permission_classes = (IsSelfOrAdmin,)
    pagination_class = CustomPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter, SearchFilter]
    filterset_class = UserFilter
    ordering_fields = ['date_joined', 'username', 'id']
    search_fields = ['real_name', 'phone']  # 只保留真实姓名和电话搜索
    ordering = ['-date_joined']  # 默认按注册时间倒序

    def get_serializer_class(self):
        """根据不同的操作返回不同的序列化器"""
        if self.action == 'create':
            return UserRegistSerializer
        if self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        return UserSerializer
    
    def get_serializer_context(self):
        """
        额外的上下文提供给序列化器，确保能够生成完整的URL
        """
        context = super().get_serializer_context()
        context.update({
            "request": self.request
        })
        return context

    def get_permissions(self):
        """
        根据不同的操作设置不同的权限
        - 注册接口允许所有人访问
        - 管理员可以访问所有用户
        - 普通用户只能访问自己的账号
        """
        if self.action == 'create':
            return [AllowAny()]
        return [IsSelfOrAdmin()]
        
    def get_queryset(self):
        """
        过滤查询结果：
        - 管理员可以查看所有用户
        - 普通用户只能查看自己
        """
        queryset = super().get_queryset()
        
        # 如果不是管理员，只能查看自己的账号
        if not self.request.user.is_staff and not self.request.user.is_superuser:
            queryset = queryset.filter(id=self.request.user.id)
            
        return queryset

    def create(self, request, *args, **kwargs):
        """
        用户注册接口
        
        参数:
        - username: 用户名 (必填)
        - password: 密码 (必填)
        - real_name: 真实姓名
        - company: 公司
        - phone: 电话
        - mobile: 手机号
        - gender: 性别 (male/female/secret)
        - department: 所属部门
        - work_department: 工作部门
        - position: 用户岗位
        - description: 用户简述
        - avatar: 用户头像
        - is_active: 是否激活
        """
        try:
            logger.info(f"Received user creation request with data: {request.data}")
            
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                logger.error(f"Validation failed with errors: {serializer.errors}")
                return Response({
                    "err_msg": serializer.errors,
                    "code": 400
                }, status=HTTP_400_BAD_REQUEST)
            
            try:
                # 保存用户
                user = serializer.save()
                username = user.username
                
                # 为用户生成证书
                try:
                    # 确保CA证书存在
                    certificate_manager.create_ca_certificate()
                    
                    # 创建用户证书
                    cert_info = certificate_manager.create_user_certificate(
                        username=username,
                        email=user.email if user.email else None
                    )
                    
                    # 更新用户证书信息
                    user.certificate_serial_number = cert_info['serial_number']
                    user.certificate_content = cert_info['cert_content']
                    user.certificate_issued_at = timezone.now()
                    # 确保过期时间是时区感知的
                    if isinstance(cert_info['not_valid_after'], datetime.datetime):
                        if timezone.is_naive(cert_info['not_valid_after']):
                            user.certificate_expires_at = timezone.make_aware(cert_info['not_valid_after'])
                        else:
                            user.certificate_expires_at = cert_info['not_valid_after']
                    else:
                        user.certificate_expires_at = None
                    user.save()
                    
                    logger.info(f"用户 {username} 创建成功，证书已颁发")
                    
                except Exception as cert_error:
                    logger.error(f"为用户 {username} 创建证书失败: {cert_error}")
                    # 证书创建失败不影响用户创建，但记录错误
                
                response_data = serializer.data.copy()
                response_data['certificate_status'] = user.get_certificate_status()
                response_data['certificate_issued'] = user.certificate_content is not None
                
                return Response({
                    "data": response_data, 
                    "msg": "用户创建成功，证书已颁发", 
                    "code": 200
                }, status=HTTP_201_CREATED)
                
            except IntegrityError as e:
                logger.error(f"Database integrity error: {str(e)}")
                return Response({
                    "err_msg": f"数据库操作异常: {str(e)}",
                    "code": 400
                }, status=HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"Unexpected error during user creation: {str(e)}")
            return Response({
                "err_msg": f"创建报错: {str(e)}",
                "code": 400
            }, status=HTTP_400_BAD_REQUEST)

    # 添加当前用户信息接口
    @action(detail=False, methods=['get'])
    def me(self, request):
        """
        获取当前登录用户的信息
        """
        try:
            user = request.user
            serializer = self.get_serializer(user)
            return Response({
                "code": 200,
                "msg": "获取个人信息成功",
                "data": serializer.data
            })
        except Exception as e:
            logger.error(f"获取个人信息失败: {str(e)}")
            return Response({
                "code": 400,
                "msg": f"获取个人信息失败: {str(e)}",
                "data": None
            }, status=HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        """
        更新用户信息接口
        
        通过PUT请求更新用户的全部信息，或通过PATCH请求更新部分信息
        
        参数:
        - username: 用户名
        - real_name: 真实姓名
        - company: 公司
        - phone: 电话
        - mobile: 手机号
        - email: 邮箱
        - gender: 性别 (male/female/secret，或使用1/2/0)
        - position: 用户岗位
        - department: 所属部门
        - description: 用户简述
        - is_active: 是否激活
        
        注意:
        - 如果传递gender字段的值为1、2、0，会自动转换为male、female、secret
        """
        try:
            # 获取要更新的用户对象
            instance = self.get_object()
            logger.info(f"正在更新用户 {instance.username} 的信息")
            
            # 预处理请求数据
            data = request.data.copy() if hasattr(request.data, 'copy') else dict(request.data)
            
            # 处理性别值
            if 'gender' in data:
                sex_mapping = {
                    '1': 'male', '2': 'female', '0': 'secret',
                    1: 'male', 2: 'female', 0: 'secret'
                }
                gender_value = data['gender']
                if gender_value in sex_mapping or str(gender_value) in sex_mapping:
                    data['gender'] = sex_mapping.get(gender_value, sex_mapping.get(str(gender_value), 'secret'))
            
            # 检查是部分更新(PATCH)还是全量更新(PUT)
            partial = kwargs.pop('partial', False)
            
            # 创建序列化器 (UserUpdateSerializer 已经排除了password字段)
            serializer = self.get_serializer(instance, data=data, partial=partial)
            
            # 验证数据
            if not serializer.is_valid():
                logger.error(f"用户更新验证失败: {serializer.errors}")
                return Response({
                    "err_msg": serializer.errors,
                    "code": 400
                }, status=HTTP_400_BAD_REQUEST)
            
            # 保存更新后的用户
            self.perform_update(serializer)
            logger.info(f"用户 {instance.username} 信息更新成功")
            
            # 返回更新后的用户信息
            return Response({
                "data": serializer.data,
                "msg": "用户信息更新成功",
                "code": 200
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"更新用户信息时发生错误: {str(e)}")
            return Response({
                "err_msg": f"更新失败: {str(e)}",
                "code": 400
            }, status=HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        username = instance.username
        print(f"删除用户: {username}")
        
        # 撤销用户证书
        try:
            certificate_manager.revoke_user_certificate(username)
            logger.info(f"用户 {username} 的证书已撤销")
        except Exception as cert_error:
            logger.error(f"撤销用户 {username} 证书失败: {cert_error}")
        
        # 删除用户
        super().destroy(request, *args, **kwargs)
            
        # 删除K8s PVC
        #del_resp = k8s_client.delete_pvc("rl-platform", username)
        #print(del_resp)
        
        return Response({"msg": "用户删除成功，证书已撤销", "code": 200}, status=HTTP_200_OK)
    
    @action(detail=True, methods=['post'])
    def regenerate_certificate(self, request, pk=None):
        """重新生成用户证书"""
        user = self.get_object()
        try:
            # 撤销旧证书
            certificate_manager.revoke_user_certificate(user.username)
            
            # 生成新证书
            cert_info = certificate_manager.create_user_certificate(
                username=user.username,
                email=user.email if user.email else None
            )
            
            # 更新用户证书信息
            user.certificate_serial_number = cert_info['serial_number']
            user.certificate_content = cert_info['cert_content']
            user.certificate_issued_at = timezone.now()
            user.certificate_expires_at = cert_info['not_valid_after']
            user.certificate_revoked = False
            user.certificate_revoked_at = None
            user.save()
            
            return Response({
                "msg": "证书重新生成成功",
                "data": {
                    "certificate_status": user.get_certificate_status(),
                    "certificate_serial_number": user.certificate_serial_number,
                    "certificate_issued_at": user.certificate_issued_at,
                    "certificate_expires_at": user.certificate_expires_at,
                }
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"重新生成证书失败: {e}")
            return Response({"err_msg": f"重新生成证书失败: {e}"}, status=HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def revoke_certificate(self, request, pk=None):
        """撤销用户证书"""
        user = self.get_object()
        try:
            # 撤销证书文件
            certificate_manager.revoke_user_certificate(user.username)
            
            # 更新数据库记录
            user.certificate_revoked = True
            user.certificate_revoked_at = timezone.now()
            user.save()
            
            return Response({
                "msg": "证书撤销成功",
                "data": {
                    "certificate_status": user.get_certificate_status(),
                    "certificate_revoked_at": user.certificate_revoked_at,
                }
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"撤销证书失败: {e}")
            return Response({"err_msg": f"撤销证书失败: {e}"}, status=HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def download_certificate(self, request, pk=None):
        """下载用户证书"""
        user = self.get_object()
        if not user.has_valid_certificate():
            return Response({"err_msg": "用户没有有效证书"}, status=HTTP_400_BAD_REQUEST)
        
        from django.http import FileResponse
        import tempfile
        import os
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.pem', delete=False) as temp_file:
                temp_file.write(user.certificate_content)
                temp_file_path = temp_file.name
            
            # 返回文件下载响应
            response = FileResponse(
                open(temp_file_path, 'rb'),
                as_attachment=True,
                filename=f'{user.username}-certificate.pem'
            )
            
            # 清理临时文件
            def cleanup():
                try:
                    os.unlink(temp_file_path)
                except OSError:
                    pass
            
            response.close = cleanup
            return response
            
        except Exception as e:
            logger.error(f"下载证书失败: {e}")
            return Response({"err_msg": f"下载证书失败: {e}"}, status=HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def download_p12_certificate(self, request, pk=None):
        """下载用户P12格式证书"""
        user = self.get_object()
        if not user.has_valid_certificate():
            return Response({"err_msg": "用户没有有效证书"}, status=HTTP_400_BAD_REQUEST)
        
        from django.http import FileResponse
        from pathlib import Path
        
        try:
            # P12证书文件路径
            p12_path = Path(certificate_manager.user_dir) / user.username / f'{user.username}.p12'
            
            if not p12_path.exists():
                return Response({"err_msg": "P12证书文件不存在"}, status=HTTP_400_BAD_REQUEST)
            
            # 返回文件下载响应
            response = FileResponse(
                open(p12_path, 'rb'),
                as_attachment=True,
                filename=f'{user.username}-certificate.p12'
            )
            
            return response
            
        except Exception as e:
            logger.error(f"下载P12证书失败: {e}")
            return Response({"err_msg": f"下载P12证书失败: {e}"}, status=HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def ca_certificate(self, request):
        """获取CA根证书"""
        try:
            ca_info = certificate_manager.get_ca_certificate_info()
            if not ca_info:
                return Response({"err_msg": "CA证书不存在"}, status=HTTP_400_BAD_REQUEST)
            
            return Response({
                "msg": "获取CA证书成功",
                "data": ca_info
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取CA证书失败: {e}")
            return Response({"err_msg": f"获取CA证书失败: {e}"}, status=HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def download_ca_certificate(self, request):
        """下载CA根证书"""
        try:
            ca_info = certificate_manager.get_ca_certificate_info()
            if not ca_info:
                return Response({"err_msg": "CA证书不存在"}, status=HTTP_400_BAD_REQUEST)
            
            from django.http import FileResponse
            import tempfile
            import os
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.pem', delete=False) as temp_file:
                temp_file.write(ca_info['cert_content'])
                temp_file_path = temp_file.name
            
            # 返回文件下载响应
            response = FileResponse(
                open(temp_file_path, 'rb'),
                as_attachment=True,
                filename='ca-certificate.pem'
            )
            
            # 清理临时文件
            def cleanup():
                try:
                    os.unlink(temp_file_path)
                except OSError:
                    pass
            
            response.close = cleanup
            return response
            
        except Exception as e:
            logger.error(f"下载CA证书失败: {e}")
            return Response({"err_msg": f"下载CA证书失败: {e}"}, status=HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def list_certificates(self, request):
        """列出所有用户证书"""
        try:
            certificates = certificate_manager.list_user_certificates()
            return Response({
                "msg": "获取证书列表成功",
                "data": certificates
            }, status=HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取证书列表失败: {e}")
            return Response({"err_msg": f"获取证书列表失败: {e}"}, status=HTTP_400_BAD_REQUEST)
            
    def list(self, request, *args, **kwargs):
        """
        获取用户列表
        """
        logger.info(f"接收到获取用户列表请求，查询参数：{request.query_params}")
        
        # 获取查询集
        queryset = self.filter_queryset(self.get_queryset())
        logger.info(f"过滤后的查询SQL: {str(queryset.query)}")
        
        # 应用分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            logger.info(f"返回分页数据，当前页数据条数：{len(page)}")
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        logger.info(f"返回所有数据，总条数：{len(queryset)}")
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """
        激活用户账户
        """
        try:
            user = self.get_object()
            user.is_active = True
            user.save()
            logger.info(f"用户 {user.username} 已被激活")
            return Response({
                "code": 200,
                "msg": "用户已激活",
                "data": {
                    "username": user.username,
                    "is_active": user.is_active
                }
            })
        except Exception as e:
            logger.error(f"激活用户失败：{str(e)}")
            return Response({
                "code": 400,
                "msg": f"激活用户失败：{str(e)}",
                "data": None
            }, status=HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """
        停用用户账户
        """
        try:
            user = self.get_object()
            user.is_active = False
            user.save()
            logger.info(f"用户 {user.username} 已被停用")
            return Response({
                "code": 200,
                "msg": "用户已停用",
                "data": {
                    "username": user.username,
                    "is_active": user.is_active
                }
            })
        except Exception as e:
            logger.error(f"停用用户失败：{str(e)}")
            return Response({
                "code": 400,
                "msg": f"停用用户失败：{str(e)}",
                "data": None
            }, status=HTTP_400_BAD_REQUEST)

    # 添加修改密码接口
    @action(detail=True, methods=['post'])
    def change_password(self, request, pk=None):
        """
        修改用户密码
        
        参数:
        - new_password: 新密码
        - confirm_password: 确认密码
        """
        try:
            user = self.get_object()
            new_password = request.data.get('new_password')
            confirm_password = request.data.get('confirm_password')
            
            # 验证参数
            if not new_password:
                return Response({
                    "code": 400,
                    "msg": "新密码不能为空",
                }, status=HTTP_400_BAD_REQUEST)
                
            if new_password != confirm_password:
                return Response({
                    "code": 400,
                    "msg": "两次输入的密码不一致",
                }, status=HTTP_400_BAD_REQUEST)
                
            # 验证密码强度
            if len(new_password) < 8:
                return Response({
                    "code": 400,
                    "msg": "密码长度至少为8位",
                }, status=HTTP_400_BAD_REQUEST)
            
            # 更新密码
            user.set_password(new_password)
            user.save()
            
            logger.info(f"用户 {user.username} 成功修改密码")
            return Response({
                "code": 200,
                "msg": "密码修改成功",
            })
            
        except Exception as e:
            logger.error(f"修改密码失败: {str(e)}")
            return Response({
                "code": 400,
                "msg": f"修改密码失败: {str(e)}",
            }, status=HTTP_400_BAD_REQUEST)

    def get_certificate_status(self):
        """获取证书状态"""
        if not self.certificate_content:
            return "未申请"
        if self.certificate_revoked:
            return "已撤销"
        if not self.certificate_expires_at:
            return "未知"
        
        # 确保使用时区感知的时间比较
        from django.utils import timezone
        now = timezone.now()
        expires_at = timezone.localtime(self.certificate_expires_at)
        if now > expires_at:
            return "已过期"
        return "有效"

class MyTokenObtainPairView(TokenObtainPairView):
    """获取token"""
    serializer_class = MyTokenObtainPairSerializer
    
    def get_serializer_context(self):
        """
        额外的上下文提供给序列化器，确保能够生成完整的URL
        """
        context = super().get_serializer_context()
        return context
