from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from user.models import User
import base64
import uuid
from django.core.files.base import ContentFile


class UserSerializer(serializers.ModelSerializer):
    certificate_status = serializers.SerializerMethodField()
    avatar_url = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        # fields = "__all__"
        # 不让看到的字段
        exclude=["user_permissions","groups"]

    def get_certificate_status(self, obj):
        """获取证书状态"""
        return obj.get_certificate_status()
        
    def get_avatar_url(self, obj):
        """返回头像URL"""
        if obj.avatar and hasattr(obj.avatar, 'url'):
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.avatar.url)
            return obj.avatar.url
        return None

    # 因为UpdateModelMixin方法修改密码的时候不会对数据进行加密,所以需要就需要我自己写一个方法对数据进行加密
    def validate(self,attrs):
        from django.contrib.auth import hashers
        if "password" in attrs:
            attrs["password"] = hashers.make_password(attrs["password"])
        return attrs


# 创建用户更新序列化器（不要求密码字段）
class UserUpdateSerializer(serializers.ModelSerializer):
    avatar_data = serializers.CharField(required=False, write_only=True)
    avatar_url = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'username', 'real_name', 'company', 'phone', 'mobile', 
            'email', 'gender', 'position', 'department', 'work_department', 
            'description', 'avatar', 'avatar_data', 'avatar_url', 'is_active'
        ]
        # 显式排除密码字段
        extra_kwargs = {
            'username': {'read_only': True},  # 用户名不允许修改
            'avatar': {'required': False}     # 头像是可选的
        }
    
    def get_avatar_url(self, obj):
        """返回头像的完整URL"""
        if obj.avatar and hasattr(obj.avatar, 'url'):
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.avatar.url)
            return obj.avatar.url
        return None

    def validate(self, attrs):
        # 处理空字符串
        for field in ['mobile', 'phone', 'real_name', 'company', 'department', 'work_department', 'position', 'description', 'email']:
            if field in attrs and attrs[field] == '':
                attrs[field] = None
                
        # 处理Base64编码的头像数据
        if 'avatar_data' in attrs and attrs['avatar_data']:
            try:
                # 处理可能的Base64前缀
                if 'data:image/' in attrs['avatar_data'] and ';base64,' in attrs['avatar_data']:
                    format_info, encoded_data = attrs['avatar_data'].split(';base64,')
                    ext = format_info.split('/')[-1]
                else:
                    encoded_data = attrs['avatar_data']
                    ext = 'png'  # 默认扩展名
                
                # 生成唯一文件名
                filename = f"{uuid.uuid4()}.{ext}"
                
                # 解码并创建文件
                decoded_data = base64.b64decode(encoded_data)
                avatar_file = ContentFile(decoded_data, name=filename)
                attrs['avatar'] = avatar_file
                
            except Exception as e:
                raise serializers.ValidationError({'avatar_data': f'无效的图像数据: {str(e)}'})
                
            # 移除临时字段
            del attrs['avatar_data']
                
        # 处理Notes字段映射到description
        if 'Notes' in self.initial_data and not attrs.get('description'):
            notes = self.initial_data.get('Notes')
            if notes:
                attrs['description'] = notes
        
        return attrs

    def to_representation(self, instance):
        """自定义返回数据"""
        ret = super().to_representation(instance)
        # 确保返回头像URL
        if 'avatar_url' not in ret or not ret['avatar_url']:
            ret['avatar_url'] = self.get_avatar_url(instance)
        return ret


# 创建用户注册序列化类
class UserRegistSerializer(serializers.ModelSerializer):
    avatar_data = serializers.CharField(required=False, write_only=True)
    avatar_url = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'username', 'password', 'real_name', 'company', 'phone', 'mobile', 
            'email', 'gender', 'position', 'department', 'work_department', 
            'description', 'avatar', 'avatar_data', 'avatar_url', 'is_active', 'is_staff', 'is_superuser'
        ]
        extra_kwargs = {
            'password': {'write_only': True},
            'username': {
                'required': True,
                'error_messages': {
                    'required': '用户名必填'
                }
            },
            'password': {
                'required': True,
                'write_only': True,
                'error_messages': {
                    'required': '密码必填'
                }
            },
            'real_name': {'required': False},
            'company': {'required': False},
            'phone': {'required': False},
            'mobile': {'required': False},
            'email': {'required': False},  # 确保email字段被正确处理
            'gender': {'required': False, 'default': 'secret'},
            'department': {'required': False},
            'work_department': {'required': False},
            'position': {'required': False},
            'description': {'required': False},
            'avatar': {'required': False},
            'is_active': {'required': False, 'default': True},
            'is_staff': {'required': False, 'default': False},
            'is_superuser': {'required': False, 'default': False}
        }
    
    def get_avatar_url(self, obj):
        """返回头像URL"""
        if obj.avatar and hasattr(obj.avatar, 'url'):
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.avatar.url)
            return obj.avatar.url
        return None
        
    def to_internal_value(self, data):
        """在字段验证之前预处理性别值"""
        # 如果存在gender字段，预处理性别值
        if 'gender' in data:
            gender_value = data['gender']
            sex_mapping = {
                '1': 'male',
                '2': 'female',
                '0': 'secret',
                1: 'male',
                2: 'female',
                0: 'secret'
            }
            
            if gender_value in sex_mapping:
                data = data.copy()  # 创建一个副本以避免修改原始数据
                data['gender'] = sex_mapping[gender_value]
                
        return super().to_internal_value(data)

    def validate_username(self, value):
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("用户名已存在")
        return value

    def validate_password(self, value):
        if len(value) < 8:
            raise serializers.ValidationError("密码长度至少为8位")
        return value
        
    def validate(self, attrs):
        # 处理空字符串
        for field in ['mobile', 'phone', 'real_name', 'company', 'department', 'work_department', 'position', 'description', 'email']:
            if field in attrs and attrs[field] == '':
                attrs[field] = None
                
        # 处理Notes字段映射到description
        if 'Notes' in self.initial_data and not attrs.get('description'):
            notes = self.initial_data.get('Notes')
            if notes:
                attrs['description'] = notes
        
        # 处理头像数据
        if 'avatar_data' in attrs and attrs['avatar_data']:
            try:
                # 处理可能的Base64前缀
                if 'data:image/' in attrs['avatar_data'] and ';base64,' in attrs['avatar_data']:
                    format_info, encoded_data = attrs['avatar_data'].split(';base64,')
                    ext = format_info.split('/')[-1]
                else:
                    encoded_data = attrs['avatar_data']
                    ext = 'png'  # 默认扩展名
                
                # 生成唯一文件名
                filename = f"{uuid.uuid4()}.{ext}"
                
                # 解码并创建文件
                decoded_data = base64.b64decode(encoded_data)
                avatar_file = ContentFile(decoded_data, name=filename)
                attrs['avatar'] = avatar_file
                
            except Exception as e:
                raise serializers.ValidationError({'avatar_data': f'无效的图像数据: {str(e)}'})
                
            # 移除临时字段
            del attrs['avatar_data']
                
        return attrs

    def create(self, validated_data):
        user = User.objects.create_user(
            username=validated_data['username'],
            password=validated_data['password'],
            real_name=validated_data.get('real_name'),
            company=validated_data.get('company'),
            phone=validated_data.get('phone'),
            mobile=validated_data.get('mobile'),
            email=validated_data.get('email'),
            gender=validated_data.get('gender', 'secret'),
            department=validated_data.get('department'),
            work_department=validated_data.get('work_department'),
            position=validated_data.get('position'),
            description=validated_data.get('description'),
            avatar=validated_data.get('avatar'),
            is_active=validated_data.get('is_active', True),
            is_staff=validated_data.get('is_staff', False),
            is_superuser=validated_data.get('is_superuser', False)
        )
        return user

