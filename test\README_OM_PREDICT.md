# OM模型推理工具 (使用本地ultralytics源码)

## 📋 概述

这是一个修改后的华为昇腾NPU OM模型推理工具，支持对图片进行目标检测推理，类似于 `yolo predict` 命令的功能。

**重要特性:**
- 🔧 使用项目中 `data/ultralytics_v8` 目录下的本地ultralytics源码
- 🚀 支持华为昇腾NPU OM模型推理
- 📊 自动验证源码路径，确保使用正确版本
- ⚡ 兼容原有验证功能

## 🔧 主要功能

### 1. 图片推理模式 (新增)
- 支持单张图片、批量图片、通配符模式推理
- 自动保存标注图片、JSON结果、裁剪检测框
- 实时显示推理进度和性能统计

### 2. 验证模式 (原有)
- 在验证数据集上评估模型性能
- 计算mAP、精度、召回率等指标

## 🚀 使用方法

### 基本语法
```bash
python test/om_infer.py --mode predict [参数]
```

### 常用示例

#### 1. 单张图片推理
```bash
python test/om_infer.py \
    --mode predict \
    --weight ./weights/yolov8n.pt \
    --om ./weights/yolov8n_bs1.om \
    --source data/test.jpg \
    --save_img \
    --save_json \
    --conf 0.5
```

#### 2. 批量图片推理
```bash
python test/om_infer.py \
    --mode predict \
    --weight ./weights/yolov8n.pt \
    --om ./weights/yolov8n_bs1.om \
    --source data/images/ \
    --save_dir runs/predict/batch \
    --save_img \
    --save_crop \
    --conf 0.3
```

#### 3. 通配符模式
```bash
python test/om_infer.py \
    --mode predict \
    --weight ./weights/yolov8n.pt \
    --om ./weights/yolov8n_bs1.om \
    --source "data/*.jpg" \
    --save_img
```

## 📊 参数说明

### 必需参数
- `--weight`: PT模型路径（用于获取类别名称和配置）
- `--om`: OM模型路径
- `--source`: 输入源（图片/目录/通配符）

### 可选参数
- `--mode`: 运行模式 (`predict` 或 `val`)
- `--save_dir`: 输出目录 (默认: `runs/predict`)
- `--imgsz`: 图像尺寸 (默认: 640)
- `--conf`: 置信度阈值 (默认: 0.5)
- `--iou`: NMS IoU阈值 (默认: 0.45)
- `--max_det`: 最大检测数量 (默认: 1000)
- `--device_id`: NPU设备ID (默认: '0')

### 保存选项
- `--save_img`: 保存标注图片
- `--save_json`: 保存JSON格式结果
- `--save_crop`: 保存裁剪的检测框

## 📁 输出文件结构

```
runs/predict/
├── image1_annotated.jpg    # 标注图片
├── image1.json            # JSON结果
├── image2_annotated.jpg
├── image2.json
└── crops/                 # 裁剪框目录
    ├── person/
    │   ├── image1_0.jpg
    │   └── image1_1.jpg
    └── car/
        └── image2_0.jpg
```

## 📄 JSON结果格式

```json
[
  {
    "bbox": [100, 150, 300, 400],
    "confidence": 0.85,
    "class": 0,
    "class_name": "person"
  },
  {
    "bbox": [450, 200, 600, 350],
    "confidence": 0.72,
    "class": 2,
    "class_name": "car"
  }
]
```

## 🔄 与YOLO命令对比

| YOLO命令 | OM推理命令 |
|----------|------------|
| `yolo predict model=best.pt source=test.jpg save conf=0.5` | `python test/om_infer.py --mode predict --weight best.pt --om best.om --source test.jpg --save_img --conf 0.5` |
| `yolo predict model=best.pt source=images/ save_crop` | `python test/om_infer.py --mode predict --weight best.pt --om best.om --source images/ --save_img --save_crop` |

## ⚡ 性能特点

- **高效推理**: 使用华为昇腾NPU加速
- **批量处理**: 支持目录和通配符批量推理
- **详细统计**: 显示预处理、推理、后处理时间
- **灵活输出**: 多种保存格式选择

## 🛠️ 环境要求

### 必需环境
- 华为昇腾NPU环境
- `ais_bench` 推理工具 (`pip install ais_bench`)
- `torch`, `cv2`, `numpy` 等依赖

### 目录结构要求
```
项目根目录/
├── test/
│   ├── om_infer.py              # 本推理脚本
│   └── README_OM_PREDICT.md     # 使用说明
├── data/
│   └── ultralytics_v8/          # 本地ultralytics源码目录
│       └── ultralytics/         # ultralytics包
│           ├── __init__.py
│           ├── nn/
│           ├── yolo/
│           └── ...
└── weights/
    ├── model.pt                 # PT模型文件
    └── model.om                 # OM模型文件
```

### 源码验证
脚本启动时会自动验证ultralytics源码路径：
- ✅ 使用本地源码：显示本地路径
- ❌ 使用系统版本：提示错误并退出

## 📝 使用示例脚本

运行示例脚本查看更多用法：
```bash
python test/om_predict_example.py
```

## 🔍 故障排除

### 常见问题

1. **找不到图片**: 检查 `--source` 路径是否正确
2. **OM模型加载失败**: 确认NPU环境和模型路径
3. **内存不足**: 减少 `--max_det` 或使用更小的图片尺寸
4. **权限错误**: 确保输出目录有写入权限

### 调试技巧

- 使用 `--conf 0.1` 降低置信度阈值查看更多检测
- 检查JSON输出确认检测结果
- 查看标注图片验证检测框位置

## 📈 性能优化建议

1. **批量推理**: 使用目录模式而非逐张推理
2. **合适阈值**: 根据应用场景调整置信度和IoU阈值
3. **图像尺寸**: 使用与训练时一致的图像尺寸
4. **NPU利用**: 确保NPU驱动和工具链版本匹配
