# 训练日志API接口文档

## 📋 概述

这些接口用于获取和实时监控训练过程中的日志信息，包括train.log文件的内容。

## 🔗 接口列表

### 1. 获取训练日志 (静态)

**接口路径**: `/backend/training/{training_id}/logs`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `lines` | int | 否 | 100 | 获取的行数 |
| `mode` | string | 否 | tail | 获取模式：`tail`(最后几行)、`head`(前几行)、`all`(全部) |
| `search` | string | 否 | - | 搜索关键词 |

#### 调用示例

```javascript
// 获取最后100行日志
fetch('/api/training/123/logs?lines=100&mode=tail', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('日志内容:', data.data.logs);
    console.log('文件信息:', data.data.log_info);
});

// 搜索包含"error"的日志
fetch('/api/training/123/logs?search=error&lines=50', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('错误日志:', data.data.logs);
});

// 获取全部日志
fetch('/api/training/123/logs?mode=all', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('完整日志:', data.data.logs);
});
```

#### 返回数据格式

```json
{
    "success": true,
    "data": {
        "training_id": 123,
        "logs": "训练日志内容...",
        "log_type": "tail",
        "lines_requested": 100,
        "search_pattern": "",
        "log_info": {
            "file_size": 1048576,
            "file_size_mb": 1.0,
            "line_count": 2500,
            "modify_time": 1703123456,
            "modify_time_str": "2023-12-21 10:30:56"
        },
        "timestamp": 1703123456.789
    }
}
```

### 2. 实时日志流 (SSE)

**接口路径**: `/backend/training/{training_id}/log-stream`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证  
**响应类型**: `text/event-stream`

#### 调用示例

```javascript
// 建立SSE连接
const eventSource = new EventSource('/backend/training/123/log-stream');

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'connected':
            console.log('日志流连接已建立');
            break;
            
        case 'initial':
            console.log('初始日志:', data.logs);
            displayLogs(data.logs);
            break;
            
        case 'update':
            console.log('日志更新:', data.logs);
            appendLogs(data.logs);
            break;
            
        case 'final':
            console.log('训练结束，最终日志:', data.logs);
            displayFinalLogs(data.logs, data.status);
            eventSource.close();
            break;
            
        case 'error':
            console.error('日志流错误:', data.message);
            break;
            
        case 'timeout':
            console.log('连接超时');
            eventSource.close();
            break;
    }
};

eventSource.onerror = function(event) {
    console.error('SSE连接错误:', event);
    eventSource.close();
};

// 手动关闭连接
function closeLogStream() {
    eventSource.close();
}
```

#### SSE事件类型

| 事件类型 | 说明 | 数据字段 |
|----------|------|----------|
| `connected` | 连接建立 | `message`, `timestamp` |
| `initial` | 初始日志 | `logs`, `timestamp` |
| `update` | 日志更新 | `logs`, `timestamp` |
| `final` | 训练结束 | `logs`, `status`, `timestamp` |
| `error` | 错误信息 | `message`, `timestamp` |
| `timeout` | 连接超时 | `message`, `timestamp` |

## 🎯 前端集成示例

### Vue.js 组件示例

```vue
<template>
  <div class="training-logs">
    <div class="log-controls">
      <button @click="fetchLogs('tail', 100)">最新100行</button>
      <button @click="fetchLogs('head', 50)">前50行</button>
      <button @click="fetchLogs('all')">全部日志</button>
      <button @click="startLogStream" :disabled="isStreaming">开始实时监控</button>
      <button @click="stopLogStream" :disabled="!isStreaming">停止监控</button>
    </div>
    
    <div class="log-search">
      <input v-model="searchKeyword" placeholder="搜索关键词" />
      <button @click="searchLogs">搜索</button>
    </div>
    
    <div class="log-info" v-if="logInfo">
      <p>文件大小: {{ logInfo.file_size_mb }}MB</p>
      <p>总行数: {{ logInfo.line_count }}</p>
      <p>最后修改: {{ logInfo.modify_time_str }}</p>
    </div>
    
    <div class="log-content">
      <pre>{{ logs }}</pre>
    </div>
  </div>
</template>

<script>
export default {
  props: ['trainingId'],
  data() {
    return {
      logs: '',
      logInfo: null,
      searchKeyword: '',
      isStreaming: false,
      eventSource: null
    }
  },
  methods: {
    async fetchLogs(mode = 'tail', lines = 100) {
      try {
        const params = new URLSearchParams({ mode, lines });
        const response = await this.$http.get(`/api/training/${this.trainingId}/logs?${params}`);
        
        this.logs = response.data.data.logs;
        this.logInfo = response.data.data.log_info;
      } catch (error) {
        console.error('获取日志失败:', error);
      }
    },
    
    async searchLogs() {
      if (!this.searchKeyword) return;
      
      try {
        const params = new URLSearchParams({ 
          search: this.searchKeyword,
          lines: 200 
        });
        const response = await this.$http.get(`/api/training/${this.trainingId}/logs?${params}`);
        
        this.logs = response.data.data.logs;
      } catch (error) {
        console.error('搜索日志失败:', error);
      }
    },
    
    startLogStream() {
      if (this.isStreaming) return;
      
      this.eventSource = new EventSource(`/api/training/${this.trainingId}/log-stream`);
      this.isStreaming = true;
      
      this.eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        switch(data.type) {
          case 'connected':
            console.log('日志流已连接');
            break;
            
          case 'initial':
            this.logs = data.logs;
            break;
            
          case 'update':
            this.logs += '\n' + data.logs;
            this.$nextTick(() => {
              // 自动滚动到底部
              const logElement = this.$el.querySelector('.log-content');
              logElement.scrollTop = logElement.scrollHeight;
            });
            break;
            
          case 'final':
            this.logs += '\n' + data.logs;
            this.stopLogStream();
            break;
            
          case 'error':
            console.error('日志流错误:', data.message);
            break;
        }
      };
      
      this.eventSource.onerror = () => {
        this.stopLogStream();
      };
    },
    
    stopLogStream() {
      if (this.eventSource) {
        this.eventSource.close();
        this.eventSource = null;
      }
      this.isStreaming = false;
    }
  },
  
  beforeDestroy() {
    this.stopLogStream();
  }
}
</script>

<style scoped>
.training-logs {
  max-width: 100%;
  margin: 20px 0;
}

.log-controls {
  margin-bottom: 10px;
}

.log-controls button {
  margin-right: 10px;
  padding: 5px 10px;
}

.log-search {
  margin-bottom: 10px;
}

.log-search input {
  padding: 5px;
  margin-right: 10px;
  width: 200px;
}

.log-info {
  background: #f5f5f5;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

.log-content {
  background: #000;
  color: #0f0;
  padding: 15px;
  border-radius: 4px;
  max-height: 500px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
```

## 🔧 功能特点

1. **多种获取模式**: 支持获取最后几行、前几行或全部日志
2. **日志搜索**: 支持关键词搜索功能
3. **实时监控**: 使用SSE实现实时日志推送
4. **文件信息**: 提供日志文件的详细信息
5. **错误处理**: 完善的错误检查和提示
6. **自动断开**: 训练结束时自动断开连接

## ⚠️ 注意事项

1. **连接限制**: SSE连接会在30分钟后自动超时
2. **性能考虑**: 获取全部日志时注意文件大小
3. **权限验证**: 所有接口都需要有效的认证token
4. **资源清理**: 前端组件销毁时记得关闭SSE连接

这些接口可以帮助你实时监控训练过程，快速定位问题和查看训练进度！
