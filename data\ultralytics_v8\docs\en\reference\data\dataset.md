---
description: Explore the YOLODataset and its subclasses for object detection, segmentation, and multi-modal tasks. Find details on dataset loading, caching, and augmentation.
keywords: Ultralytics, YOLODataset, object detection, segmentation, dataset loading, caching, data augmentation
---

# Reference for `ultralytics/data/dataset.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/dataset.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/dataset.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/dataset.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.data.dataset.YOLODataset

<br><br><hr><br>

## ::: ultralytics.data.dataset.YOLOMultiModalDataset

<br><br><hr><br>

## ::: ultralytics.data.dataset.GroundingDataset

<br><br><hr><br>

## ::: ultralytics.data.dataset.YOLOConcatDataset

<br><br><hr><br>

## ::: ultralytics.data.dataset.SemanticDataset

<br><br><hr><br>

## ::: ultralytics.data.dataset.ClassificationDataset

<br><br>
