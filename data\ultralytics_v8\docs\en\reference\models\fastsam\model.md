---
description: Discover how to use the FastSAM model with Ultralytics. Learn about its interface and implementation details with practical examples.
keywords: FastSAM, Ultralytics, model interface, YOLO, deep learning, machine learning, segmentation, predictor, validator, Python
---

# Reference for `ultralytics/models/fastsam/model.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/fastsam/model.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/fastsam/model.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/fastsam/model.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.fastsam.model.FastSAM

<br><br>
