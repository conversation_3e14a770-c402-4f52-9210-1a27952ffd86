---
description: Explore the detailed API reference for Ultralytics SAM/AMG models, including functions for mask stability scores, crop box generation, and more.
keywords: Ultralytics, SAM, AMG, API Reference, models, mask stability, crop boxes, data processing, YOLO
---

# Reference for `ultralytics/models/sam/amg.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/amg.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/amg.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/amg.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.sam.amg.is_box_near_crop_edge

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.batch_iterator

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.calculate_stability_score

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.build_point_grid

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.build_all_layer_point_grids

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.generate_crop_boxes

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.uncrop_boxes_xyxy

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.uncrop_points

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.uncrop_masks

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.remove_small_regions

<br><br><hr><br>

## ::: ultralytics.models.sam.amg.batched_mask_to_box

<br><br>
