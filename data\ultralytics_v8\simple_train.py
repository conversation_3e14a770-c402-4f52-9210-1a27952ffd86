#!/usr/bin/env python3
"""
最简单的YOLO训练脚本 - 直接使用源码
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# 设置环境变量
os.environ["PYTHONPATH"] = str(current_dir)

try:
    # 导入YOLO
    from ultralytics import YOLO
    
    print("🚀 开始YOLO训练...")
    print(f"📁 工作目录: {current_dir}")
    
    # 创建YOLO模型
    model = YOLO("yolo11n.pt")
    
    print("📦 模型加载成功！")
    print("🎯 开始训练...")
    
    # 训练设置
    results = model.train(
        data="coco8.yaml",       # 数据集配置
        epochs=5,                # 训练轮次（设为5轮快速测试）
        batch=4,                 # 批次大小（较小适合CPU）
        imgsz=320,               # 图像尺寸（较小适合CPU）
        device="cpu",            # 使用CPU
        project="runs/train",    # 保存目录
        name="simple_test",      # 实验名称
        verbose=True,            # 详细输出
        save=True,               # 保存模型
        plots=True,              # 保存图表
        val=True,                # 验证
        patience=10,             # 早停耐心
        cache=False,             # 不使用缓存
        workers=1,               # 数据加载线程数
    )
    
    print("✅ 训练完成！")
    print(f"📈 训练结果保存在: runs/train/simple_test/")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保在ultralytics源码目录中运行此脚本")
    
except Exception as e:
    print(f"❌ 训练出错: {e}")
    import traceback
    traceback.print_exc() 