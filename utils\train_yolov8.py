#!/usr/bin/env python3
"""
华为NPU单机单卡YOLO训练脚本
"""

import os
import time
import json
import logging
import psutil
import argparse
import subprocess
from pathlib import Path
from ultralytics import YOLO
import torch

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def detect_device():
    """
    检测可用的训练设备
    返回: 设备标识符 ('cpu', 'cuda:0', 或 'npu:0')
    """
    try:
        # 首先检查NPU
        try:
            import torch_npu
            import torch
            if torch_npu.npu.is_available():
                logger.info("发现可用的NPU设备")
                torch_npu.npu.set_device(0)
                return 'npu:0'
        except ImportError:
            logger.warning("未安装torch_npu")
            pass
        
        # 检查GPU
        import torch
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            if device_count > 0:
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"使用GPU: {gpu_name}")
                return 'cuda:0'
                
        logger.info("使用CPU进行训练")
        return 'cpu'
    except Exception as e:
        logger.warning(f"设备检测出错: {e}")
        return 'cpu'

def get_resource_usage():
    """
    获取系统资源使用情况
    返回: 包含CPU、内存、GPU和NPU使用率的字典
    """
    usage = {
        'cpu_usage': psutil.cpu_percent(),
        'memory_usage': psutil.virtual_memory().percent,
        'gpu_usage': 0.0,
        'npu_usage': 0.0,
        'timestamp': time.time()
    }
    
    # 获取GPU使用率
    try:
        import torch
        if torch.cuda.is_available():
            usage['gpu_usage'] = float(torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100)
    except:
        pass
    
    # 获取NPU使用率 这块可以优化，目前是根据npu-smi的输出计算使用率，后续可以优化
    try:
        result = subprocess.check_output(['npu-smi', 'info'], universal_newlines=True)
        for line in result.split('\n'):
            if '910' in line and 'OK' in line:
                parts = line.split('|')
                if len(parts) >= 4:
                    power_info = parts[3].strip()
                    if 'W' in power_info:
                        power = float(power_info.split('W')[0].strip())
                        usage['npu_usage'] = (power / 250.0) * 100  # 假设最大功率250W
    except:
        pass
    
    return usage

def parse_training_config(config, dataset_path=None):
    """
    解析训练配置，转换为YOLOv8可接受的参数格式
    """
    print("config", config)
    # 基础训练参数
    train_args = {
        'epochs': int(config['parameters']['epochs']), # 训练轮数
        'device': detect_device(), # 训练设备
        'project': 'runs_detect', # 训练结果保存路径
        'name': f'task_{config["id"]}', # 训练结果保存名称
        'exist_ok': True, # 是否覆盖训练结果
        'plots': False,  # 禁用绘图以提高性能
    }
    
    # 数据集路径 - 优先使用命令行参数提供的路径
    if dataset_path:
        train_args['data'] = dataset_path
    else:
        # 数据集和验证参数
        dataset_dir = os.path.join('datasets', config['training']['dataset']['name']) # 数据集路径
        train_args['data'] = os.path.join(dataset_dir, 'data.yaml') # 数据集路径   
    
    #train_args['val'] = float(config['training'].get('validationRatio', 0.2)) # 验证比例
    
    # 批次大小和学习率
    train_args['batch'] = int(config['parameters'].get('batchSize', 16)) # 批次大小
    train_args['lr0'] = float(config['parameters'].get('learningRate', 0.01)) # 学习率  
    
    # 优化器相关参数
    optimizer = config['otherParams'].get('optimizer', 'SGD').lower() # 优化器
    train_args['optimizer'] = optimizer # 优化器
    
    if optimizer in ['sgd', 'adam', 'adamw']:
        train_args['momentum'] = float(config['otherParams'].get('momentum', 0.937)) # 动量
        train_args['weight_decay'] = float(config['otherParams'].get('weightDecay', 0.0005)) # 权重衰减 
    
    #if optimizer in ['adam', 'adamw']:
    #    train_args['beta1'] = 0.9  # Adam beta1
    #    train_args['beta2'] = 0.999  # Adam beta2
        
    # 早停和检查点
    if 'earlyStopping' in config['otherParams']:
        train_args['patience'] = int(config['otherParams']['earlyStopping'])
    
    if 'checkpointFreq' in config['otherParams']:
        train_args['save_period'] = int(config['otherParams']['checkpointFreq'])
    
    # 混合精度训练
    if config['otherParams'].get('useMixedPrecision', False):
        train_args['amp'] = True  # 自动混合精度
    
    # 学习率策略
    lr_strategy = config['parameters'].get('learningRateStrategy', '').lower()
    if lr_strategy == '余弦衰减':
        train_args['cos_lr'] = True  # 使用余弦学习率调度
    
    # 梯度裁剪
    #if config['otherParams'].get('useGradientClipping', False):
    #    max_norm = float(config['parameters'].get('maxGradNorm', 10.0))
    #    train_args['max_grad_norm'] = max_norm
    
    # 标签平滑
    if 'labelSmoothing' in config['otherParams']:
        train_args['label_smoothing'] = float(config['otherParams']['labelSmoothing'])
    
    # Dropout
    if 'dropout' in config['otherParams']:
        train_args['dropout'] = float(config['otherParams']['dropout'])
    
    # 预热步数
    if 'warmupSteps' in config['otherParams']:
        warmup_epochs = int(config['otherParams']['warmupSteps']) // (train_args['batch'] * 100)  # 估算预热轮数
        train_args['warmup_epochs'] = max(1, warmup_epochs)  # 至少1轮
        train_args['warmup_momentum'] = 0.8
        train_args['warmup_bias_lr'] = 0.1
    
    # 计算类型
    #compute_type = config['parameters'].get('computeType', '').lower()
    #if compute_type == 'bf16':
    #    train_args['bf16'] = True  # bfloat16 训练
    
    return train_args

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='YOLOv8 训练脚本')
    parser.add_argument('--config', type=str, default='training_config.json', help='训练配置文件路径')
    parser.add_argument('--model', type=str, help='模型路径，优先级高于配置文件')
    parser.add_argument('--data', type=str, help='数据集路径，优先级高于配置文件')
    parser.add_argument('--task_id', type=str, help='任务ID，用于生成输出目录名')
    return parser.parse_args()

def main():
    """主函数：加载配置并开始训练"""
    try:
        # 解析命令行参数
        args = parse_args()
        
        # 加载训练配置
        with open(args.config, 'r') as f:
            config = json.load(f)
        
        # 如果提供了任务ID，更新配置
        if args.task_id:
            config['id'] = args.task_id
            
        # 解析训练参数
        train_args = parse_training_config(config, args.data)
        logger.info(f"训练参数: {json.dumps(train_args, indent=2)}")
        
        # 设置模型路径 - 优先使用命令行参数提供的路径
        if args.model:
            model_path = args.model
        else:
            model_path = config['algorithm']['modelPath']
            
        if not os.path.exists(model_path):
            logger.warning(f"未找到本地模型: {model_path}，使用预训练模型")
            model_path = 'yolov8n.pt'
        
        # 创建模型
        model = YOLO(model_path)
        
        # 添加训练回调函数
        def on_train_epoch_end(trainer):
            """每个训练周期结束时的回调函数"""
            metrics = trainer.metrics
            resource_metrics = get_resource_usage()
            
            # 从trainer.tloss获取训练损失组件
            if hasattr(trainer, 'tloss') and len(trainer.tloss) >= 3:
                # 将tensor转换为Python的float值
                box_loss = round(float(trainer.tloss[0]), 4)
                cls_loss = round(float(trainer.tloss[1]), 4)
                dfl_loss = round(float(trainer.tloss[2]), 4)
            else:
                # 如果tloss不可用或格式不符，则使用默认值或尝试从metrics获取
                box_loss = metrics.get('train/box_loss', 0.0)
                cls_loss = metrics.get('train/cls_loss', 0.0)
                dfl_loss = metrics.get('train/dfl_loss', 0.0)
            
            # 合并训练指标和资源使用指标
            combined_metrics = {
                'epoch': trainer.epoch,
                'train/box_loss': box_loss,
                'train/cls_loss': cls_loss,
                'train/dfl_loss': dfl_loss,
                'metrics/precision': metrics.get('metrics/precision(B)', 0.0),
                'metrics/recall': metrics.get('metrics/recall(B)', 0.0),
                'metrics/mAP50': metrics.get('metrics/mAP50(B)', 0.0),
                'metrics/mAP50-95': metrics.get('metrics/mAP50-95(B)', 0.0),
                **resource_metrics
            }
            
            # 保存指标到文件
            with open('/workspace/training_metrics.json', 'a') as f:
                f.write(json.dumps(combined_metrics) + '\n')
        
        # 注册回调函数
        model.add_callback('on_train_epoch_end', on_train_epoch_end)
        
        # 开始训练
        try:
            model.train(**train_args)
        except Exception as e:
            logger.error(f"训练失败: {e}")
            raise
            
    except Exception as e:
        logger.error(f"训练初始化失败: {e}")
        raise

if __name__ == '__main__':
    main()