#!/usr/bin/env python3
"""
华为NPU多机多卡YOLO训练脚本
支持单机多卡和多机多卡训练
"""

import sys
import os
import argparse
from pathlib import Path

def find_ultralytics_source():
    """智能查找ultralytics_v8源码目录"""
    current_dir = Path(__file__).parent.absolute()
    
    # 可能的源码路径列表
    possible_paths = [
        current_dir / "data" / "ultralytics_v8",                    # 标准项目结构
        current_dir / ".." / "data" / "ultralytics_v8",             # 脚本在子目录中
        current_dir / ".." / ".." / "data" / "ultralytics_v8",      # 脚本在深层子目录中
        current_dir / "ultralytics_v8",                            # 脚本与源码同级
        current_dir / ".." / "ultralytics_v8",                     # 脚本在源码子目录中
        current_dir.parent / "data" / "ultralytics_v8",            # 备用路径1
        Path(__file__).parent.parent / "data" / "ultralytics_v8",  # 备用路径2
    ]
    
    for path in possible_paths:
        ultralytics_module = path / "ultralytics"
        if ultralytics_module.exists() and (ultralytics_module / "__init__.py").exists():
            print(f"✅ 找到ultralytics源码: {path}")
            return path.resolve()
    
    print("❌ 未找到ultralytics_v8源码目录")
    print("🔍 请确保以下路径之一存在并包含ultralytics源码:")
    for path in possible_paths:
        print(f"   - {path.resolve()}")
    
    return None

def setup_ultralytics_path():
    """设置ultralytics源码路径并移除系统安装版本"""
    print("🔧 设置ultralytics源码路径...")
    
    # 1. 移除所有可能的系统ultralytics路径
    paths_to_remove = []
    for path in sys.path[:]:  # 创建副本避免迭代时修改
        path_lower = str(path).lower()
        if ('ultralytics' in path_lower and 
            ('site-packages' in path_lower or 'dist-packages' in path_lower)):
            paths_to_remove.append(path)
    
    for path in paths_to_remove:
        try:
            sys.path.remove(path)
            print(f"🧹 移除系统ultralytics路径: {path}")
        except ValueError:
            pass
    
    # 2. 查找并添加本地源码路径
    ultralytics_dir = find_ultralytics_source()
    if ultralytics_dir is None:
        print("❌ 无法找到本地ultralytics源码，请检查目录结构")
        sys.exit(1)
    
    # 3. 将本地源码路径插入到最前面
    ultralytics_str = str(ultralytics_dir)
    if ultralytics_str in sys.path:
        sys.path.remove(ultralytics_str)
    sys.path.insert(0, ultralytics_str)
    
    # 4. 设置环境变量
    os.environ["PYTHONPATH"] = ultralytics_str
    
    print(f"📁 设置源码路径: {ultralytics_dir}")
    return ultralytics_dir

# 在导入任何其他模块之前先设置路径
setup_ultralytics_path()

def is_distributed_run():
    """检测是否通过torch.distributed.run启动"""
    # 检查关键的分布式环境变量
    rank = os.environ.get('RANK')
    local_rank = os.environ.get('LOCAL_RANK')
    world_size = os.environ.get('WORLD_SIZE')
    master_addr = os.environ.get('MASTER_ADDR')
    master_port = os.environ.get('MASTER_PORT')
    
    # 如果这些变量都存在，说明是通过torch.distributed.run启动的
    distributed = all([rank is not None, local_rank is not None, world_size is not None])
    
    if distributed:
        print("🌐 检测到分布式训练环境 (torch.distributed.run)")
        print(f"   RANK = {rank}")
        print(f"   LOCAL_RANK = {local_rank}")
        print(f"   WORLD_SIZE = {world_size}")
        print(f"   MASTER_ADDR = {master_addr}")
        print(f"   MASTER_PORT = {master_port}")
        return True
    else:
        print("🔧 单机训练模式")
        return False

def setup_npu_environment():
    """设置NPU训练环境"""
    try:
        import torch_npu
        print(f"✅ torch_npu版本: {torch_npu.__version__}")
        
        # 检查NPU可用性
        if not torch_npu.npu.is_available():
            raise RuntimeError("NPU不可用，请检查驱动和torch_npu安装")
        
        npu_count = torch_npu.npu.device_count()
        print(f"✅ 检测到 {npu_count} 个NPU设备")
        
        # 显示NPU信息
        for i in range(npu_count):
            device_name = torch_npu.npu.get_device_name(i)
            device_props = torch_npu.npu.get_device_properties(i)
            memory_gb = device_props.total_memory / 1024**3
            print(f"   NPU:{i} - {device_name} ({memory_gb:.1f}GB)")
        
        return npu_count
        
    except ImportError:
        raise ImportError("torch_npu未安装，请先安装华为NPU工具包")
    except Exception as e:
        raise RuntimeError(f"NPU环境检查失败: {e}")

def verify_ultralytics_source():
    """验证ultralytics源码路径"""
    try:
        # 验证ultralytics模块导入
        import ultralytics
        ultralytics_path = Path(ultralytics.__file__).parent
        
        # 获取已设置的源码路径
        expected_dir = find_ultralytics_source()
        if expected_dir is None:
            print("❌ 无法找到期望的源码路径")
            return False
            
        expected_path = expected_dir / "ultralytics"
        
        print(f"🔍 ultralytics导入路径: {ultralytics_path}")
        print(f"🎯 期望的源码路径: {expected_path}")
        
        # 检查是否使用了本地源码
        if expected_path.resolve() == ultralytics_path.resolve():
            print("✅ 正在使用本地修改的ultralytics源码")
            return True
        else:
            print("⚠️  使用的不是本地源码，可能使用了系统安装的版本")
            print("💡 建议：重新运行脚本，确保路径设置正确")
            return False
            
    except ImportError as e:
        print(f"❌ ultralytics导入失败: {e}")
        return False

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='华为NPU多机多卡YOLO训练')
    
    # 基本训练参数
    parser.add_argument('--model', type=str, default='yolo11n.pt', help='模型文件路径')
    parser.add_argument('--data', type=str, default='coco8.yaml', help='数据集配置文件')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮次')
    parser.add_argument('--batch', type=int, default=16, help='批次大小')
    parser.add_argument('--imgsz', type=int, default=640, help='图像尺寸')
    parser.add_argument('--lr0', type=float, default=0.01, help='初始学习率')
    
    # 设备配置
    parser.add_argument('--device', type=str, default='auto', 
                       help='设备配置: auto(自动), npu:0(单NPU), npu:0,1(多NPU), npu:0,1,2,3(4NPU)')
    
    # 分布式训练参数 (仅在非torch.distributed.run模式下使用)
    parser.add_argument('--multi-machine', action='store_true', help='启用多机训练')
    parser.add_argument('--master-addr', type=str, default='127.0.0.1', help='主节点IP地址')
    parser.add_argument('--master-port', type=str, default='29500', help='主节点端口')
    parser.add_argument('--world-size', type=int, default=1, help='总进程数(节点数*每节点NPU数)')
    parser.add_argument('--rank', type=int, default=0, help='当前节点在所有节点中的排名')
    
    # 保存配置
    parser.add_argument('--project', type=str, default='runs/train', help='项目目录')
    parser.add_argument('--name', type=str, default='npu_train', help='实验名称')
    parser.add_argument('--exist-ok', action='store_true', help='允许覆盖现有结果')
    
    # 性能优化
    parser.add_argument('--amp', action='store_true', help='启用自动混合精度训练')
    parser.add_argument('--workers', type=int, default=8, help='数据加载线程数')
    parser.add_argument('--cache', action='store_true', help='启用数据缓存')
    
    return parser.parse_args()

def get_device_config(device_arg, npu_count):
    """根据参数获取设备配置"""
    if device_arg == 'auto':
        if npu_count > 1:
            device = f"npu:{','.join(map(str, range(npu_count)))}"
            print(f"🔄 自动配置: 使用所有{npu_count}个NPU - {device}")
        else:
            device = "npu:0"
            print("🔄 自动配置: 使用单个NPU - npu:0")
    else:
        device = device_arg
        print(f"🔧 手动配置: {device}")
    
    return device

def distributed_train(args, device):
    """分布式训练 (通过torch.distributed.run启动)"""
    # 确保从本地源码导入YOLO
    try:
        # 验证导入路径
        import ultralytics
        print(f"✅ 使用ultralytics源码路径: {ultralytics.__file__}")
        
        from ultralytics import YOLO
        print(f"✅ 成功从本地源码导入YOLO")
        
    except ImportError as e:
        print(f"❌ 导入YOLO失败: {e}")
        print("请确保data/ultralytics_v8目录存在且包含完整的源码")
        sys.exit(1)
    
    # 获取分布式信息
    rank = int(os.environ.get('RANK', 0))
    local_rank = int(os.environ.get('LOCAL_RANK', 0))
    world_size = int(os.environ.get('WORLD_SIZE', 1))
    
    # 只在rank 0显示详细信息
    if rank == 0:
        print("=" * 60)
        print("🌐 启动分布式NPU训练")
        print(f"🌍 世界大小: {world_size}")
        print(f"📍 全局排名: {rank}")
        print(f"📱 本地排名: {local_rank}")
        print(f"💾 设备: {device}")
        print(f"📊 批次大小: {args.batch}")
        print(f"🔄 训练轮次: {args.epochs}")
        print("=" * 60)
    
    # 对于多机多卡，设备需要调整为对应local_rank的NPU
    if ',' in device:
        # 多NPU情况下，使用local_rank对应的NPU
        npu_list = device.replace('npu:', '').split(',')
        if local_rank < len(npu_list):
            actual_device = f"npu:{npu_list[local_rank]}"
        else:
            actual_device = f"npu:{local_rank}"
    else:
        # 单NPU情况
        actual_device = f"npu:{local_rank}"
    
    if rank == 0:
        print(f"🎯 当前进程使用设备: {actual_device}")
    
    # 创建模型
    model = YOLO(args.model)
    
    # 训练配置
    train_args = {
        'data': args.data,
        'epochs': args.epochs,
        'batch': args.batch,
        'imgsz': args.imgsz,
        'device': actual_device,  # 使用调整后的设备
        'lr0': args.lr0,
        'project': args.project,
        'name': args.name,
        'exist_ok': args.exist_ok,
        'amp': args.amp,
        'workers': args.workers,
        'cache': args.cache,
        'verbose': rank == 0,  # 只在rank 0显示详细输出
        'save': rank == 0,     # 只在rank 0保存模型
        'plots': rank == 0,    # 只在rank 0生成图表
        'val': rank == 0,      # 只在rank 0进行验证
    }
    
    if rank == 0:
        print("🎯 开始分布式训练...")
    
    results = model.train(**train_args)
    
    if rank == 0:
        print("✅ 分布式训练完成！")
        print(f"📈 结果保存在: {model.trainer.save_dir}")
    
    return results

def single_machine_train(args, device):
    """单机训练（单卡或多卡）"""
    # 确保从本地源码导入YOLO
    try:
        # 验证导入路径
        import ultralytics
        print(f"✅ 使用ultralytics源码路径: {ultralytics.__file__}")
        
        from ultralytics import YOLO
        print(f"✅ 成功从本地源码导入YOLO")
        
    except ImportError as e:
        print(f"❌ 导入YOLO失败: {e}")
        print("请确保data/ultralytics_v8目录存在且包含完整的源码")
        sys.exit(1)
    
    print("=" * 60)
    print("🚀 启动单机NPU训练")
    print(f"📱 设备: {device}")
    print(f"📊 批次大小: {args.batch}")
    print(f"🔄 训练轮次: {args.epochs}")
    print("=" * 60)
    
    # 创建模型
    model = YOLO(args.model)
    
    # 训练配置
    train_args = {
        'data': args.data,
        'epochs': args.epochs,
        'batch': args.batch,
        'imgsz': args.imgsz,
        'device': device,
        'lr0': args.lr0,
        'project': args.project,
        'name': args.name,
        'exist_ok': args.exist_ok,
        'amp': args.amp,
        'workers': args.workers,
        'cache': args.cache,
        'verbose': True,
        'save': True,
        'plots': True,
        'val': True,
    }
    
    print("🎯 开始训练...")
    results = model.train(**train_args)
    
    print("✅ 训练完成！")
    print(f"📈 结果保存在: {model.trainer.save_dir}")
    return results

def multi_machine_train_setup(args, device):
    """多机训练设置 (手动模式，不推荐)"""
    print("⚠️  警告：您正在使用手动多机模式，推荐使用torch.distributed.run")
    print("=" * 60)
    print("🌐 配置多机多卡训练")
    print(f"🏠 主节点: {args.master_addr}:{args.master_port}")
    print(f"🔢 总进程数: {args.world_size}")
    print(f"📍 当前节点排名: {args.rank}")
    print(f"📱 设备: {device}")
    print("=" * 60)
    
    # 设置分布式环境变量
    os.environ['MASTER_ADDR'] = args.master_addr
    os.environ['MASTER_PORT'] = args.master_port
    os.environ['WORLD_SIZE'] = str(args.world_size)
    os.environ['RANK'] = str(args.rank)
    
    # 如果是多NPU设备，设置LOCAL_RANK
    if ',' in device:
        npu_list = device.replace('npu:', '').split(',')
        os.environ['LOCAL_RANK'] = '0'  # 主进程
    else:
        os.environ['LOCAL_RANK'] = '0'
    
    print("🔧 环境变量设置完成:")
    print(f"   MASTER_ADDR = {os.environ['MASTER_ADDR']}")
    print(f"   MASTER_PORT = {os.environ['MASTER_PORT']}")
    print(f"   WORLD_SIZE = {os.environ['WORLD_SIZE']}")
    print(f"   RANK = {os.environ['RANK']}")
    print(f"   LOCAL_RANK = {os.environ['LOCAL_RANK']}")
    
    return single_machine_train(args, device)

def print_training_guide():
    """打印训练指南"""
    print("\n" + "=" * 80)
    print("📖 华为NPU多机多卡训练指南")
    print("=" * 80)
    
    print("\n🔹 单机单卡训练:")
    print("   python npu_multi_train.py --device npu:0")
    
    print("\n🔹 单机多卡训练:")
    print("   python npu_multi_train.py --device npu:0,1,2,3")
    
    print("\n🔹 多机多卡训练 (推荐使用torch.distributed.run):")
    print("   # 机器0:")
    print("   python -m torch.distributed.run --nproc_per_node=1 --nnodes=2 \\")
    print("          --node_rank=0 --master_addr=********** --master_port=29500 \\")
    print("          npu_multi_train.py --device npu:0")
    print("   # 机器1:")
    print("   python -m torch.distributed.run --nproc_per_node=1 --nnodes=2 \\")
    print("          --node_rank=1 --master_addr=********** --master_port=29500 \\")
    print("          npu_multi_train.py --device npu:0")
    
    print("\n💡 性能优化建议:")
    print("   - 使用 --amp 启用混合精度训练")
    print("   - 调整 --batch 大小以充分利用NPU内存")
    print("   - 使用 --cache 缓存数据集以提高训练速度")
    print("   - 确保网络带宽足够支持多机通信")
    
    print("\n⚠️  注意事项:")
    print("   - 确保所有机器能互相访问主节点IP")
    print("   - 所有机器的代码和数据集路径要一致")
    print("   - 批次大小必须能被NPU总数整除")
    print("   - 多机训练时只在主节点(rank=0)显示输出")

def main():
    """主函数"""
    print("🚀 华为NPU多机多卡YOLO训练")
    print(f"📁 工作目录: {Path(__file__).parent.absolute()}")
    
    # 检测是否通过torch.distributed.run启动
    is_distributed = is_distributed_run()
    
    # 在解析参数之前先验证源码导入
    print("\n" + "="*60)
    print("🔍 强制验证ultralytics源码导入")
    print("="*60)
    if not verify_ultralytics_source():
        print("\n❌ 源码验证失败！必须使用本地修改的ultralytics源码")
        print("📋 问题原因：程序仍在使用系统安装的ultralytics包")
        print("🔧 解决方案：")
        print("   1. 卸载系统ultralytics: pip uninstall ultralytics")
        print("   2. 确保data/ultralytics_v8目录存在且包含完整源码")
        print("   3. 重新运行此脚本")
        print("\n💡 提示：NPU多卡训练需要修改后的ultralytics源码才能正常工作")
        sys.exit(1)
    
    print("✅ 源码验证通过，正在使用本地修改版本\n")
    
    args = parse_arguments()
    
    try:
        
        # 检查NPU环境
        print("\n" + "="*50)
        print("🔧 检查NPU环境")
        print("="*50)
        npu_count = setup_npu_environment()
        
        # 配置设备
        print("\n" + "="*50)
        print("⚙️ 配置训练设备")
        print("="*50)
        device = get_device_config(args.device, npu_count)
        
        # 根据运行模式选择训练方式
        if is_distributed:
            # 通过torch.distributed.run启动的分布式训练
            results = distributed_train(args, device)
        elif args.multi_machine:
            # 手动多机训练模式
            results = multi_machine_train_setup(args, device)
        else:
            # 单机训练
            # 检查batch size是否合理
            if ',' in device:
                device_count = len(device.replace('npu:', '').split(','))
                if args.batch % device_count != 0:
                    print(f"⚠️  警告: batch={args.batch} 不能被NPU数量{device_count}整除")
                    suggested_batch = (args.batch // device_count + 1) * device_count
                    print(f"💡 建议使用 batch={suggested_batch}")
                    
                    response = input("是否使用建议的batch size? (y/n): ")
                    if response.lower() == 'y':
                        args.batch = suggested_batch
                        print(f"✅ 已调整batch size为: {args.batch}")
            
            results = single_machine_train(args, device)
            
        print("\n✅ 所有训练任务完成！")
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        print_training_guide()
        sys.exit(1)

if __name__ == "__main__":
    main() 