import logging
import uuid
import json
import os
import time
import tempfile
import paramiko
from pathlib import Path
from typing import Dict, Optional, List
from utils.resource_manager import ResourceManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__) 

class YOLOv8DockerTrainer:
    """
    YOLOv8训练器类 - 基于资源调度平台的容器化训练解决方案
    
    特性：
    - 支持NPU和GPU训练环境
    - 实时状态监控和日志获取
    
    前端配置文件格式示例:
    {
        "id": "task_001",                # 任务ID
        "algorithm": {
            "version": "v8",             # 算法版本
            "modelPath": "yolov8n.pt"    # 模型路径
        },
        "training": {
            "dataset": {
                "id": 1,                 # 数据集ID
                "name": "coco"           # 数据集名称
            },
            "validationRatio": 0.2       # 验证集比例
        },
        "parameters": {
            "epochs": 100,               # 训练轮数
            "batchSize": 16,             # 批次大小
            "learningRate": 0.01,        # 学习率
            "learningRateStrategy": "余弦衰减",  # 学习率策略
            "maxGradNorm": 10.0,         # 最大梯度范数
            "computeType": "float32"     # 计算类型 (float32/bf16)
        },
        "otherParams": {
            "optimizer": "SGD",          # 优化器类型 (SGD/Adam/AdamW)
            "momentum": 0.937,           # 动量
            "weightDecay": 0.0005,       # 权重衰减
            "earlyStopping": 10,         # 早停轮数
            "checkpointFreq": 10,        # 检查点保存频率
            "warmupSteps": 1000,         # 预热步数
            "labelSmoothing": 0.1,       # 标签平滑
            "dropout": 0.2,              # Dropout比例
            "useMixedPrecision": true,   # 是否使用混合精度训练
            "useGradientClipping": true  # 是否使用梯度裁剪
        },
        "resources": {
            "cpu": 4,                    # CPU核心数
            "npu": 1,                    # NPU数量
            "storage": 100,              # 存储大小(GB)
            "specification_id": null,     # 可选，指定产品ID
            "mirror_image": null,        # 可选，指定镜像名称
            "storage_id": null,          # 可选，指定存储ID
            "structure": "amd64"         # 系统架构(amd64/arm64)
        }
    }
    
    注意事项:
    1. 所有数值类型参数都应该以字符串形式传递
    2. 资源配置中的specification_id, mirror_image, storage_id 是可选的
    3. 如果不指定具体的资源ID，系统会自动选择合适的资源
    4. 数据集路径应该遵循 datasets/{dataset_name}/data.yaml 的格式
    5. 模型路径可以是预训练模型名称或本地路径
    6. 容器启动时会自动执行训练任务，无需SSH连接
    7. 支持实时状态监控和日志查看
    """
    
    def __init__(self, training_task):
        """
        初始化训练器
        参数:
            training_task: TrainingTask模型实例，包含以下主要字段：
                - algorithm_version: 算法版本 (v8)
                - model_path: 模型路径
                - dataset_id: 数据集ID
                - dataset_name: 数据集名称
                - cpu_count: CPU数量
                - npu_count: NPU数量
                - storage_size: 存储大小
                - learning_rate: 学习率
                - epochs: 训练轮数
                - batch_size: 批处理大小
                - optimizer: 优化器
                - momentum: 动量
                - weight_decay: 权重衰减
                等...
        """
        self.training_task = training_task
        self.task_info = None
        
        # 通过资源调度平台请求资源
        self.resource_manager = ResourceManager()
        
        # 训练脚本和配置文件的本地路径
        self.train_script_path = os.path.join(os.path.dirname(__file__), 'train_yolov8.py')
        self.ssh_client = None
        self.remote_dir = "/workspace"
        
        # 存储最近获取的训练指标
        self.last_metrics = None
        self.metrics_cache_time = 0
        self.metrics_cache_timeout = 30  # 30秒缓存有效期

    def _find_product_by_id(self, products: list, specification_id: str) -> Optional[dict]:
        """根据规格ID查找产品"""
        for product in products:
            if product.get('specificationId') == specification_id:
                return product
        return None

    def _find_image_by_name(self, images: list, image_name: str) -> Optional[str]:
        """根据镜像名称查找镜像"""
        for image in images:
            if image == image_name:
                return image
        return None

    def _request_resource(self) -> bool:
        """请求训练资源"""
        try:
            # 1. 查询可用产品
            products = self.resource_manager.query_products()
            if not products:
                logger.error("没有可用的计算资源")
                return False

            # 获取配置参数
            resources = {
                'cpu': int(self.training_task.cpu_count),
                'npu': int(self.training_task.npu_count),
                'specification_id': getattr(self.training_task, 'specification_id', None),
                'mirror_image': getattr(self.training_task, 'mirror_image', None),
                'structure': getattr(self.training_task, 'structure', 'amd64')
            }

            # 选择产品
            selected_product = None
            if resources.get('specification_id'):
                selected_product = self._find_product_by_id(products, resources['specification_id'])
                if not selected_product:
                    logger.error(f"未找到指定的产品ID: {resources['specification_id']}")
                    return False
            else:
                # 获取第一个可用产品
                for item in products:
                    if item.get('hoseAlias') == 'node01-910B3':
                        continue
                    else:
                        selected_product = item
                        break

            # 2. 查询镜像信息
            images = self.resource_manager.query_images(selected_product.get('structure', 'amd64'))
            if not images:
                logger.error("没有可用的镜像")
                return False

            selected_image = None
            if resources.get('mirror_image'):
                selected_image = self._find_image_by_name(images, resources['mirror_image'])
                if not selected_image:
                    logger.error(f"未找到指定的镜像: {resources['mirror_image']}")
                    return False
            else:
                # 选择包含深度学习环境的镜像
                for image in images:
                    if any(keyword in image.lower() for keyword in ['pytorch', 'ultralytics', 'yolov8:', 'torch']):
                        selected_image = image
                        break
                if not selected_image:
                    selected_image = images[0]

            # 3. 创建任务
            taskname = f"yolov8训练任务-{uuid.uuid4().hex[:8]}"
            
            # 使用NPU数量作为specification_amount参数
            specification_amount = int(resources.get('npu', 1))  # 默认为1
            
            task = self.resource_manager.create_task(
                calculate_device_id=selected_product['calculateDeviceId'],
                specification_id=selected_product['specificationId'],
                mirror_image=selected_image,
                task_name=taskname,
                gpu_mode=selected_product['gpuMode'],
                specification_amount=specification_amount  # 传递NPU数量
            )

            if task.get("code") != "0":
                logger.error("任务创建失败")
                return False

            # 4. 查询任务状态获取详细信息
            task_info = self.resource_manager.query_task(taskname)
            if not task_info or not task_info.get('records'):
                logger.error("获取任务信息失败")
                return False

            self.task_info = {
                'taskName': taskname,
                'taskId': task_info['records'][0]['id'],
                'ip': task_info['records'][0]['agentIp'],
                'port': task_info['records'][0]['sshPort'],
                'password': task_info['records'][0]['sshPasswd'],
                'status': task_info['records'][0]['status'],
            }

            logger.info(f"成功创建训练任务，任务ID: {task_info['records'][0]['id']}")
            return True

        except Exception as e:
            logger.error(f"请求资源失败: {e}")
            return False

    def _connect_ssh(self):
        """连接到远程服务器"""
        if not self.task_info:
            logger.error("没有任务信息，无法连接SSH")
            return False

        try:
            # 创建SSH客户端
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接远程服务器
            logger.info(f"正在连接服务器: {self.task_info['ip']}:{self.task_info['port']}")
            self.ssh_client.connect(
                hostname=self.task_info['ip'],
                port=int(self.task_info['port']),
                username='root',
                password=self.task_info['password'],
                timeout=30
            )
            logger.info("SSH连接成功")
            return True
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            return False

    def _upload_training_files(self):
        """上传训练所需的文件"""
        if not self.ssh_client:
            logger.error("SSH未连接，无法上传文件")
            return False

        temp_path = None
        try:
            # 确保远程工作目录存在
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)
            
            # 创建SFTP客户端
            sftp = self.ssh_client.open_sftp()
            
            # 上传训练脚本
            logger.info(f"上传训练脚本: {self.train_script_path}")
            remote_script_path = f"{self.remote_dir}/train_yolov8.py"
            sftp.put(self.train_script_path, remote_script_path)
            
            # 上传torch_utils.py到远程服务器的ultralytics包中
            local_torch_utils_path = os.path.join(os.getcwd(), "data", "ultralytics_v8", "ultralytics", "utils", "torch_utils.py")
            remote_torch_utils_dir = "/usr/local/python3.9.2/lib/python3.9/site-packages/ultralytics/utils/"
            
            # 上传文件
            if os.path.exists(local_torch_utils_path):
                logger.info(f"上传torch_utils.py: {local_torch_utils_path} -> {remote_torch_utils_dir}")
                remote_torch_utils_path = f"{remote_torch_utils_dir}/torch_utils.py"
                sftp.put(local_torch_utils_path, remote_torch_utils_path)
                logger.info("torch_utils.py上传成功")
            else:
                logger.warning(f"本地文件不存在: {local_torch_utils_path}")
            
            # 创建并上传训练配置文件
            config = self._generate_training_config()
            print("配置文件：", config)
            
            # 使用tempfile模块创建临时文件
            fd, temp_path = tempfile.mkstemp(suffix='.json', prefix='training_config_')
            try:
                with os.fdopen(fd, 'w') as temp:
                    json.dump(config, temp, indent=2)
                
                logger.info(f"创建临时配置文件: {temp_path}")
                
                # 上传临时文件
                remote_config_path = f"{self.remote_dir}/training_config.json"
                sftp.put(temp_path, remote_config_path)
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    logger.info(f"已删除临时文件: {temp_path}")
            
            # 关闭SFTP连接
            sftp.close()
            
            logger.info("训练文件上传成功")
            return True
        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            # 确保清理临时文件
            if temp_path and os.path.exists(temp_path):
                os.remove(temp_path)
                logger.info(f"异常处理中删除临时文件: {temp_path}")
            return False

    def _execute_training_script(self):
        """执行远程训练脚本"""
        if not self.ssh_client:
            logger.error("SSH未连接，无法执行训练脚本")
            return False

        try:
            # 1. 确保远程工作目录存在
            logger.info(f"创建远程工作目录: {self.remote_dir}")
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)
            
            # 2. 准备训练目录
            logger.info("准备训练目录...")
            prep_dirs_cmd = f"mkdir -p {self.remote_dir}/runs_detect"
            self.ssh_client.exec_command(prep_dirs_cmd)
            
            # 3. 通过query_bucket_list获取挂载信息
            logger.info("获取存储挂载信息...")
            
            # 使用已创建资源的任务信息查询详细信息
            task_info = self.resource_manager.query_task(self.task_info['taskName'])
            if not task_info or not task_info.get('records'):
                logger.error("无法获取任务信息")
                return False
            
            # 从任务信息中获取calculateDeviceId
            calculate_device_id = task_info['records'][0].get('calculateDeviceId')
            if not calculate_device_id:
                logger.error("无法获取calculateDeviceId")
                return False
            
            # 查询存储挂载信息
            buckets = self.resource_manager.query_bucket_list(calculate_device_id)
            if not buckets:
                logger.error("无法获取存储挂载信息")
                return False
                
            # 4. 查找挂载路径
            user_data_mount_path = None
            
            for bucket in buckets:
                mount_path = bucket.get('mountPath', '')
                # 查找用户数据挂载路径
                if "siton-data-" in mount_path:
                    user_data_mount_path = mount_path
                    break
            
            # 如果没找到特定挂载点，使用默认路径
            if not user_data_mount_path:
                user_data_mount_path = "/root/siton-data-b496463103254f46976c4ff88ea74bc9"
            
            # 构建模型和数据集目录
            # 对于特定的挂载路径，添加models和datasets子目录
            if "siton-data-" in user_data_mount_path:
                model_mount_path = f"{user_data_mount_path}/models"
                dataset_mount_path = f"{user_data_mount_path}/datasets"
            else:
                # 对其他路径使用之前的逻辑
                model_mount_path = user_data_mount_path
                dataset_mount_path = user_data_mount_path
            
            if not model_mount_path:
                model_mount_path = "/root/siton-data/models"  # 默认模型路径
            
            # 拼接最终的数据集和模型路径
            dataset_path = f"{dataset_mount_path}/{self.training_task.dataset_name}/data.yaml"
            model_path = f"{model_mount_path}/{self.training_task.model_path}"
            logger.info(f"============数据集路径：{dataset_path}")
            logger.info(f"============模型路径：{model_path}")
            
            # 5. 生成任务ID并构建命令
            task_id = f"task_{uuid.uuid4().hex[:8]}"
            
            # 构建命令行，传递模型和数据集路径参数
            command = f"cd {self.remote_dir} && python train_yolov8.py --model {model_path} --data {dataset_path} --task_id {task_id} --config training_config.json"
            logger.info(f"执行训练命令: {command}")
            logger.info(f"数据集路径: {dataset_path}")
            logger.info(f"模型路径: {model_path}")
            
            # # 增加环境变量
            # path_cmd = "source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh"
            # stdin, stdout, stderr = self.ssh_client.exec_command(path_cmd)
            # # 检查是否有错误
            # err = stderr.read().decode().strip()
            # if err:
            #     logger.error(f"增加环境变量出错: {err}")
            #     return False
            
            # 使用nohup后台运行训练脚本，防止SSH断开导致训练中断
            run_cmd = f"nohup bash -c 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH && {command}' > {self.remote_dir}/train.log 2>&1 &"
            stdin, stdout, stderr = self.ssh_client.exec_command(run_cmd)
            
            # 检查是否有错误
            err = stderr.read().decode().strip()
            if err:
                logger.error(f"执行训练命令出错: {err}")
                return False
            
            logger.info("训练脚本已在后台启动")
            return True
        except Exception as e:
            logger.error(f"执行训练脚本失败: {e}")
            return False

    def _generate_training_config(self) -> dict:
        """生成训练配置"""
        return {
            'algorithm': {
                'version': getattr(self.training_task, 'algorithm_version', 'v8'),
                'modelPath': getattr(self.training_task, 'model_path', 'yolov8n.pt')
            },
            'training': {
                'dataset': {
                    'name': getattr(self.training_task, 'dataset_name', ''),
                    'path': f"/workspace/datasets/{getattr(self.training_task, 'dataset_name', '')}/data.yaml"
                },
                'validationRatio': float(getattr(self.training_task, 'validation_ratio', 0.2))
            },
            'parameters': {
                'learningRate': getattr(self.training_task, 'learning_rate', 0.01),
                'epochs': getattr(self.training_task, 'epochs', 100),
                'batchSize': getattr(self.training_task, 'batch_size', 16),
                'learningRateStrategy': getattr(self.training_task, 'learning_rate_strategy', '余弦衰减'),
                'computeType': getattr(self.training_task, 'compute_type', 'float32')
            },
            'otherParams': {
                'optimizer': getattr(self.training_task, 'optimizer', 'SGD'),
                'momentum': getattr(self.training_task, 'momentum', 0.937),
                'weightDecay': getattr(self.training_task, 'weight_decay', 0.0005),
                'useGradientClipping': getattr(self.training_task, 'use_gradient_clipping', True),
                'useMixedPrecision': getattr(self.training_task, 'use_mixed_precision', False)
            },
            'resources': {
                'cpuCount': getattr(self.training_task, 'cpu_count', 4),
                'npuCount': getattr(self.training_task, 'npu_count', 1)
            }
        }

    def get_training_metrics(self, force_refresh=False) -> Dict:
        """
        从远程服务器获取训练指标数据
        Args:
            force_refresh: 是否强制刷新缓存
        返回:
            dict: 包含训练指标的字典，如果获取失败则返回空字典
        """
        # 检查缓存是否有效 (如果不强制刷新)
        current_time = time.time()
        if not force_refresh and self.last_metrics is not None and (current_time - self.metrics_cache_time) < self.metrics_cache_timeout:
            logger.info("使用缓存的训练指标数据:")
            logger.info(self.last_metrics)
            return self.last_metrics
            
        # 如果没有任务信息或SSH连接，返回空字典
        if not self.task_info or not self.ssh_client:
            logger.warning("无法获取训练指标：任务未启动或SSH未连接")
            return {}
            
        try:
            # 检查metrics文件是否存在
            check_cmd = f"test -f {self.remote_dir}/training_metrics.json && echo 'exists' || echo 'not exists'"
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            result = stdout.read().decode().strip()
            
            if result != 'exists':
                logger.warning("训练指标文件不存在")
                return {}
                
            # 读取训练指标文件
            read_cmd = f"cat {self.remote_dir}/training_metrics.json"
            stdin, stdout, stderr = self.ssh_client.exec_command(read_cmd)
            
            # 解析JSON数据
            metrics_json = stdout.read().decode()
            if not metrics_json:
                logger.warning("训练指标文件为空")
                return {}
                
            try:
                # 处理可能包含多行JSON的情况
                metrics_lines = metrics_json.strip().split('\n')
                metrics_list = []
                
                for line in metrics_lines:
                    if line.strip():
                        try:
                            metric_data = json.loads(line)
                            metrics_list.append(metric_data)
                        except json.JSONDecodeError:
                            logger.warning(f"无法解析训练指标行: {line}")
                
                if not metrics_list:
                    logger.warning("未找到有效的训练指标数据")
                    return {}
                
                # 提取训练指标并组织成合适的格式
                epochs = []
                train_losses = []
                val_losses = []
                timestamps = []
                cpu_usages = []
                gpu_usages = []
                npu_usages = []
                memory_usages = []
                
                for metric in metrics_list:
                    # 收集训练指标
                    epochs.append(metric.get('epoch', 0))
                    
                    # 损失指标
                    train_box_loss = metric.get('train/box_loss', 0.0)
                    train_cls_loss = metric.get('train/cls_loss', 0.0)
                    train_dfl_loss = metric.get('train/dfl_loss', 0.0)
                    # 计算总训练损失
                    total_train_loss = train_box_loss + train_cls_loss + train_dfl_loss
                    train_losses.append(total_train_loss)
                    
                    # 使用mAP作为验证损失（实际YOLOv8不直接提供val_loss）
                    val_losses.append(1.0 - metric.get('metrics/mAP50-95', 0.0))
                    
                    # 资源使用指标
                    cpu_usages.append(metric.get('cpu_usage', 0.0))
                    gpu_usages.append(metric.get('gpu_usage', 0.0))
                    npu_usages.append(metric.get('npu_usage', 0.0))
                    memory_usages.append(metric.get('memory_usage', 0.0))
                    
                    # 时间戳
                    timestamps.append(metric.get('timestamp', current_time))
                
                # 构建返回的指标数据
                metrics_data = {
                    'epochs': epochs,
                    'train_losses': train_losses,
                    'val_losses': val_losses,
                    'timestamps': timestamps,
                    'cpu_usages': cpu_usages,
                    'gpu_usages': gpu_usages,
                    'npu_usages': npu_usages,
                    'memory_usages': memory_usages,
                    'precision': metrics_list[-1].get('metrics/precision', 0.0),
                    'recall': metrics_list[-1].get('metrics/recall', 0.0),
                    'mAP50': metrics_list[-1].get('metrics/mAP50', 0.0),
                    'mAP50-95': metrics_list[-1].get('metrics/mAP50-95', 0.0)
                }
                
                # 更新缓存
                self.last_metrics = metrics_data
                self.metrics_cache_time = current_time
                
                return metrics_data
                
            except Exception as parse_error:
                logger.error(f"解析训练指标数据时发生错误: {parse_error}")
                return {}
                
        except Exception as e:
            logger.error(f"获取训练指标失败: {e}")
            return {}
    
    def save_training_metrics_to_db(self, force_refresh=False):
        """
        将训练指标保存到数据库
        Args:
            force_refresh: 是否强制刷新缓存获取最新指标
        """
        try:
            # 导入模型
            from backend_api.models.training import TrainingMetrics
            from django.utils import timezone
            
            # 获取训练指标，可选强制刷新
            metrics_data = self.get_training_metrics(force_refresh=force_refresh)
            if not metrics_data or not metrics_data.get('epochs'):
                logger.warning("没有指标数据可保存")
                return False
                
            # 获取最新的指标记录数
            epochs = metrics_data.get('epochs', [])
            train_losses = metrics_data.get('train_losses', [])
            val_losses = metrics_data.get('val_losses', [])
            cpu_usages = metrics_data.get('cpu_usages', [])
            gpu_usages = metrics_data.get('gpu_usages', [])
            npu_usages = metrics_data.get('npu_usages', [])
            memory_usages = metrics_data.get('memory_usages', [])
            
            if not epochs:
                return False
                
            # 获取已保存的最大轮数
            latest_epoch_in_db = TrainingMetrics.objects.filter(
                task=self.training_task
            ).order_by('-epoch').values_list('epoch', flat=True).first()
            
            # 只保存新的指标
            saved_count = 0
            for i, epoch in enumerate(epochs):
                if latest_epoch_in_db is None or epoch > latest_epoch_in_db:
                    pass  # 继续执行后面的保存逻辑
                else:
                    continue # 跳过已保存的epoch
                    
                # 确保索引有效
                if i < len(train_losses) and i < len(val_losses) and i < len(cpu_usages):
                    TrainingMetrics.objects.create(
                        task=self.training_task,
                        epoch=epoch,
                        train_loss=train_losses[i],
                        val_loss=val_losses[i],
                        cpu_usage=cpu_usages[i],
                        gpu_usage=gpu_usages[i] if i < len(gpu_usages) else 0.0,
                        npu_usage=npu_usages[i] if i < len(npu_usages) else 0.0,
                        memory_usage=memory_usages[i] if i < len(memory_usages) else 0.0,
                        timestamp=timezone.now()
                    )
                    saved_count += 1
                    
            logger.info(f"已保存 {saved_count} 条新的训练指标记录")
            return saved_count > 0
                    
        except Exception as e:
            logger.error(f"保存训练指标到数据库失败: {e}")
            return False

    def get_task_status(self) -> Dict:
        """获取任务状态"""
        try:
            if not self.task_info:
                return {'status': 'not_started'}

            # 通过资源调度平台API查询任务状态
            task_info = self.resource_manager.query_task(self.task_info['taskName'])
            if not task_info or not task_info.get('records'):
                return {'status': 'unknown', 'error': '无法获取任务信息'}

            task_status = task_info['records'][0].get('status')
            
            status_mapping = {
                1: 'downloading',  # 镜像下载中
                2: 'packaging',    # 镜像打包中
                3: 'creating',     # 创建中
                4: 'running',      # 运行中
                5: 'shutting_down', # 关机中
                6: 'shutdown',     # 已关机
                7: 'unavailable',  # 不可用
                8: 'releasing',    # 释放中
                9: 'no_card_mode', # 无卡模式
                10: 'starting',    # 正在开机
                11: 'restarting',  # 重启中
                12: 'starting_no_card', # 无卡模式启动中
                13: 'resetting',   # 重置系统中
                14: 'upgrading'    # 升配中
            }
            
            # 获取资源平台状态
            status_str = status_mapping.get(task_status, 'unknown')
            
            # 如果任务正在运行，尝试检查训练是否完成
            if status_str == 'running' and self.ssh_client:
                try:
                    # 获取训练配置的总轮数
                    total_epochs = self.training_task.epochs
                    
                    # 检查训练指标文件是否存在
                    check_cmd = f"test -f {self.remote_dir}/training_metrics.json && echo 'exists' || echo 'not exists'"
                    stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
                    result = stdout.read().decode().strip()
                    
                    if result == 'exists':
                        # 读取训练指标文件
                        read_cmd = f"cat {self.remote_dir}/training_metrics.json"
                        stdin, stdout, stderr = self.ssh_client.exec_command(read_cmd)
                        metrics_json = stdout.read().decode()
                        
                        # 解析JSON数据
                        if metrics_json:
                            metrics_lines = metrics_json.strip().split('\n')
                            if metrics_lines:
                                # 获取最后一行的指标数据
                                try:
                                    last_metric = json.loads(metrics_lines[-1])
                                    current_epoch = last_metric.get('epoch', 0)
                                    
                                    # 检查是否已完成所有训练轮数
                                    if current_epoch >= int(total_epochs) - 1:  # epoch从0开始计数
                                        logger.info(f"训练已完成! 当前epoch: {current_epoch}, 总epochs: {total_epochs}")
                                        status_str = 'completed'
                                        
                                        # 强制刷新缓存获取最新训练指标
                                        logger.info("训练完成，强制刷新指标缓存获取最新数据")
                                        self.last_metrics = None  # 清除缓存
                                        latest_metrics = self.get_training_metrics(force_refresh=True)
                                        
                                        # 保存最终训练指标到数据库
                                        if latest_metrics:
                                            self.save_training_metrics_to_db(force_refresh=True)
                                except Exception as e: 
                                    logger.error(f"解析训练指标失败: {e}")
                except Exception as e:
                    logger.error(f"检查训练完成状态时出错: {e}")
            
            # 如果任务仍在运行状态，尝试保存训练指标
            if status_str == 'running':
                self.save_training_metrics_to_db(force_refresh=False)  # 正常状态下不强制刷新
            
            return {
                'status': status_str,
                'task_id': self.task_info['taskId'],
                'ip': self.task_info['ip'],
                'port': self.task_info['port']
            }
                
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {'status': 'unknown', 'error': str(e)}

    def get_training_logs(self, lines=100):
        """获取训练日志"""
        if not self.task_info or not self.ssh_client:
            return "任务未启动或SSH未连接"
            
        try:
            # 确保工作目录存在
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)
            
            # 检查日志文件是否存在
            check_cmd = f"test -f {self.remote_dir}/train.log && echo 'exists' || echo 'not exists'"
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            result = stdout.read().decode().strip()
            
            if result != 'exists':
                return "训练日志文件不存在，可能训练尚未开始或日志未生成"
            
            # 获取日志文件的最后几行
            command = f"tail -n {lines} {self.remote_dir}/train.log"
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            
            # 读取输出
            logs = stdout.read().decode()
            return logs if logs else "暂无日志"
        except Exception as e:
            logger.error(f"获取训练日志失败: {e}")
            return f"获取日志失败: {str(e)}"

    def start(self) -> bool:
        """启动训练任务"""
        try:
            # 1. 请求资源创建任务
            if not self._request_resource():
                return False
            
            # 2. 等待一段时间，确保服务器启动完成
            logger.info("等待服务器启动...")
            time.sleep(10)
            
            # 3. 建立SSH连接
            if not self._connect_ssh():
                return False
                
            # 4. 上传训练文件
            if not self._upload_training_files():
                return False
                
            # 5. 执行训练脚本
            if not self._execute_training_script():
                return False
                
            logger.info("训练任务已成功启动")
            return True
            
        except Exception as e:
            logger.error(f"启动训练任务失败: {e}")
            return False

    def stop(self) -> bool:
        """停止训练任务"""
        try:
            if not self.task_info:
                logger.warning("没有运行中的任务")
                return True

            # 先通过SSH停止训练进程
            if self.ssh_client:
                try:
                    self.ssh_client.exec_command("pkill -f train_yolov8.py")
                    logger.info("已发送停止训练进程命令")
                    # 关闭SSH连接
                    self.ssh_client.close()
                    self.ssh_client = None
                except:
                    logger.warning("停止训练进程失败，继续关闭任务")

            # 通过资源调度平台停止任务
            success = self.resource_manager.stop_task(self.task_info['taskId'])
            if success.get("code") == "0":
                logger.info(f"任务 {self.task_info['taskId']} 已停止")
                # 删除任务
                self.resource_manager.delete_task(self.task_info['taskId'])
                self.task_info = None
                return True
            else:
                logger.error(f"停止任务 {self.task_info['taskId']} 失败")
                return False
            
        except Exception as e:
            logger.error(f"停止训练任务失败: {e}")
            return False

    def get_task_id(self) -> Optional[str]:
        """获取任务ID"""
        return self.task_info.get('taskId') if self.task_info else None

    def get_task_info(self) -> Optional[Dict]:
        """获取完整的任务信息"""
        if not self.task_info:
            return None
            
        try:
            # 从资源调度平台获取最新的任务信息
            task_info = self.resource_manager.query_task(self.task_info['taskId'])
            if task_info and task_info.get('records'):
                return task_info['records'][0]
            return None
        except Exception as e:
            logger.error(f"获取任务信息失败: {e}")
            return self.task_info