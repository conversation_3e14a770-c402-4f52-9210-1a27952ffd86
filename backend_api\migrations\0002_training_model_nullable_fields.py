# Generated migration for training model nullable fields

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('backend_api', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TrainingModel',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='模型ID')),
                ('task_id', models.IntegerField(blank=True, null=True, verbose_name='训练任务ID', help_text='关联的训练任务ID')),
                ('model_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='模型名称', help_text='任务ID+模型文件名')),
                ('model_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='模型文件路径', help_text='.pt文件的完整路径')),
                ('accuracy', models.FloatField(blank=True, default=0.0, null=True, verbose_name='准确率', help_text='模型准确率 (mAP50)')),
                ('precision', models.FloatField(blank=True, default=0.0, null=True, verbose_name='精度', help_text='模型精度')),
                ('recall', models.FloatField(blank=True, default=0.0, null=True, verbose_name='召回率', help_text='模型召回率')),
                ('inference_speed', models.FloatField(blank=True, default=0.0, null=True, verbose_name='推理速度', help_text='推理速度 (FPS)')),
                ('inference_time_ms', models.FloatField(blank=True, default=0.0, null=True, verbose_name='推理时间', help_text='单次推理时间 (毫秒)')),
                ('model_size_mb', models.FloatField(blank=True, default=0.0, null=True, verbose_name='模型大小', help_text='模型文件大小 (MB)')),
                ('export_status', models.CharField(choices=[('pending', '待导出'), ('exporting', '导出中'), ('completed', '已完成'), ('failed', '导出失败')], default='pending', max_length=20, verbose_name='导出状态')),
                ('om_model_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='OM模型路径', help_text='转换后的.om文件路径')),
                ('num_classes', models.IntegerField(blank=True, default=0, null=True, verbose_name='类别数量', help_text='模型支持的类别数量')),
                ('architecture', models.CharField(blank=True, max_length=100, null=True, verbose_name='模型架构', help_text='如YOLOv8n, YOLOv8s等')),
                ('fitness', models.FloatField(blank=True, default=0.0, null=True, verbose_name='适应度分数', help_text='综合评估分数')),
                ('created_time', models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, blank=True, null=True, verbose_name='更新时间')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注', help_text='模型相关备注信息')),
                ('validation_error', models.TextField(blank=True, null=True, verbose_name='验证错误', help_text='模型验证时的错误信息')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
            ],
            options={
                'verbose_name': '训练模型',
                'verbose_name_plural': '训练模型',
                'db_table': 'training_models',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='ModelExportLog',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('status', models.CharField(blank=True, choices=[('started', '开始导出'), ('processing', '处理中'), ('completed', '导出完成'), ('failed', '导出失败')], max_length=20, null=True, verbose_name='导出状态')),
                ('start_time', models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('export_format', models.CharField(blank=True, default='om', max_length=10, null=True, verbose_name='导出格式')),
                ('export_device', models.CharField(blank=True, default='npu', max_length=50, null=True, verbose_name='导出设备')),
                ('output_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='输出路径')),
                ('file_size_mb', models.FloatField(blank=True, default=0.0, null=True, verbose_name='文件大小(MB)')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('log_content', models.TextField(blank=True, null=True, verbose_name='导出日志')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='操作者')),
                ('training_model', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='export_logs', to='backend_api.trainingmodel', verbose_name='训练模型')),
            ],
            options={
                'verbose_name': '模型导出日志',
                'verbose_name_plural': '模型导出日志',
                'db_table': 'model_export_logs',
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='ModelInferenceLog',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('input_source', models.CharField(blank=True, max_length=500, null=True, verbose_name='输入源', help_text='图片路径或数据源')),
                ('inference_time_ms', models.FloatField(blank=True, null=True, verbose_name='推理时间(毫秒)')),
                ('confidence_threshold', models.FloatField(blank=True, default=0.5, null=True, verbose_name='置信度阈值')),
                ('detections_count', models.IntegerField(blank=True, default=0, null=True, verbose_name='检测数量')),
                ('result_data', models.JSONField(blank=True, default=dict, null=True, verbose_name='推理结果', help_text='检测结果的JSON数据')),
                ('output_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='结果输出路径')),
                ('created_time', models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True, verbose_name='推理时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='操作者')),
                ('training_model', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='inference_logs', to='backend_api.trainingmodel', verbose_name='训练模型')),
            ],
            options={
                'verbose_name': '模型推理日志',
                'verbose_name_plural': '模型推理日志',
                'db_table': 'model_inference_logs',
                'ordering': ['-created_time'],
            },
        ),
        migrations.AddIndex(
            model_name='trainingmodel',
            index=models.Index(fields=['task_id'], name='training_mo_task_id_idx'),
        ),
        migrations.AddIndex(
            model_name='trainingmodel',
            index=models.Index(fields=['created_by'], name='training_mo_created_idx'),
        ),
        migrations.AddIndex(
            model_name='trainingmodel',
            index=models.Index(fields=['export_status'], name='training_mo_export_idx'),
        ),
        migrations.AddIndex(
            model_name='trainingmodel',
            index=models.Index(fields=['created_time'], name='training_mo_time_idx'),
        ),
    ]
