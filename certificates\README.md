# RL Platform 证书体系

## 概述

RL Platform 集成了完整的PKI证书体系，为每个用户提供数字证书服务。该系统包括：

- **CA根证书**: 用于签发和验证用户证书
- **用户证书**: 为每个用户颁发的个人身份证书
- **证书管理**: 证书生成、撤销、续期等管理功能

## 目录结构

```
certificates/
├── ca/                    # CA证书存储目录
│   ├── ca-cert.pem       # CA根证书
│   └── ca-key.pem        # CA私钥
├── users/                # 用户证书存储目录
│   └── [username]/       # 用户证书目录
│       ├── [username]-cert.pem  # 用户证书
│       ├── [username]-key.pem   # 用户私钥
│       └── [username].p12       # PKCS#12格式证书
└── README.md             # 本文档
```

## 功能特性

### 1. 自动证书颁发
- 用户注册时自动生成证书
- 支持PEM和PKCS#12格式
- 证书有效期1年

### 2. 证书管理
- 证书生成和撤销
- 证书状态监控
- 过期证书提醒

### 3. API接口
- 证书下载接口
- 证书验证接口
- 证书管理接口

## 使用方法

### 初始化CA证书

首次使用前需要初始化CA根证书：

```bash
python manage.py init_ca
```

强制重新创建CA证书：

```bash
python manage.py init_ca --force
```

### 为现有用户生成证书

为指定用户生成证书：

```bash
python manage.py generate_user_certificates --username [用户名]
```

为所有用户生成证书：

```bash
python manage.py generate_user_certificates --all
```

重新生成已有证书：

```bash
python manage.py generate_user_certificates --all --regenerate
```

### API接口使用

#### 1. 获取用户信息（包含证书状态）

```http
GET /api/users/{user_id}/
Authorization: Bearer {token}
```

响应示例：
```json
{
  "id": 1,
  "username": "testuser",
  "certificate_status": "有效",
  "certificate_serial_number": "123456789",
  "certificate_issued_at": "2024-01-01T00:00:00Z",
  "certificate_expires_at": "2025-01-01T00:00:00Z"
}
```

#### 2. 下载用户证书

```http
GET /api/users/{user_id}/download_certificate/
Authorization: Bearer {token}
```

#### 3. 下载P12格式证书

```http
GET /api/users/{user_id}/download_p12_certificate/
Authorization: Bearer {token}
```

#### 4. 重新生成证书

```http
POST /api/users/{user_id}/regenerate_certificate/
Authorization: Bearer {token}
```

#### 5. 撤销证书

```http
POST /api/users/{user_id}/revoke_certificate/
Authorization: Bearer {token}
```

#### 6. 获取CA根证书

```http
GET /api/users/ca_certificate/
Authorization: Bearer {token}
```

#### 7. 下载CA根证书

```http
GET /api/users/download_ca_certificate/
Authorization: Bearer {token}
```

#### 8. 列出所有证书

```http
GET /api/users/list_certificates/
Authorization: Bearer {token}
```

## 证书配置

证书相关配置在 `utils/certificate_manager.py` 中：

```python
# 证书配置
self.ca_common_name = "RL Platform Root CA"
self.organization_name = "RL Platform"
self.country_name = "CN"
self.state_name = "Beijing"
self.locality_name = "Beijing"
```

### 证书参数

- **CA证书有效期**: 10年
- **用户证书有效期**: 1年
- **密钥长度**: 2048位RSA
- **签名算法**: SHA256

## 安全考虑

1. **私钥保护**: CA私钥存储在服务器上，需要妥善保护
2. **访问控制**: 证书下载需要管理员权限
3. **证书撤销**: 支持证书撤销功能
4. **定期更新**: 建议定期更新CA证书和用户证书

## 故障排除

### 常见问题

1. **CA证书不存在**
   ```bash
   python manage.py init_ca
   ```

2. **用户证书生成失败**
   - 检查CA证书是否存在
   - 检查文件系统写入权限
   - 查看日志文件

3. **证书下载失败**
   - 确认用户有有效证书
   - 检查文件路径是否正确

### 日志查看

证书相关操作会记录在Django日志中：

```python
logger = logging.getLogger(__name__)
```

## 维护建议

1. **定期备份**: 备份CA证书和私钥
2. **监控过期**: 监控证书过期时间
3. **更新证书**: 在证书过期前及时更新
4. **安全审计**: 定期检查证书使用情况

## 集成示例

### 客户端证书验证

```python
from utils.certificate_manager import certificate_manager

# 验证证书
is_valid, message = certificate_manager.verify_certificate(cert_content)
if is_valid:
    print("证书验证成功")
else:
    print(f"证书验证失败: {message}")
```

### 在浏览器中安装证书

1. 下载P12格式证书
2. 双击证书文件
3. 按照向导完成安装
4. 在浏览器中选择证书进行身份验证

## 支持

如有问题请联系系统管理员或查看项目文档。 