# NPU多机多卡训练脚本配置文件修改总结

## 修改目标
参考 `utils/train_yolov8.py` 训练脚本，修改 `npu_multi_train.py` 的训练参数传递方式，从命令行参数改为配置文件方式。

## 主要修改内容

### 1. 添加配置文件解析功能
参考 `utils/train_yolov8.py` 中的 `parse_training_config()` 函数，添加了完整的配置文件解析功能：

```python
def parse_training_config(config, dataset_path=None, device=None):
    """
    解析训练配置，转换为YOLOv8可接受的参数格式
    """
    # 基础训练参数
    train_args = {
        'epochs': int(config['parameters']['epochs']),
        'device': device or detect_device(),
        'project': 'runs_detect',
        'name': f'task_{config.get("id", "npu_train")}',
        'exist_ok': True,
        'plots': False,
    }
    
    # 数据集路径
    if dataset_path:
        train_args['data'] = dataset_path
    else:
        dataset_dir = os.path.join('datasets', config['training']['dataset']['name'])
        train_args['data'] = os.path.join(dataset_dir, 'data.yaml')
    
    # 批次大小和学习率
    train_args['batch'] = int(config['parameters'].get('batchSize', 16))
    train_args['lr0'] = float(config['parameters'].get('learningRate', 0.01))
    train_args['imgsz'] = int(config['parameters'].get('imageSize', 640))
    
    # 优化器相关参数
    optimizer = config['otherParams'].get('optimizer', 'SGD').lower()
    train_args['optimizer'] = optimizer
    
    if optimizer in ['sgd', 'adam', 'adamw']:
        train_args['momentum'] = float(config['otherParams'].get('momentum', 0.937))
        train_args['weight_decay'] = float(config['otherParams'].get('weightDecay', 0.0005))
    
    # 早停和检查点
    if 'earlyStopping' in config['otherParams']:
        train_args['patience'] = int(config['otherParams']['earlyStopping'])
    
    if 'checkpointFreq' in config['otherParams']:
        train_args['save_period'] = int(config['otherParams']['checkpointFreq'])
    
    # 混合精度训练
    if config['otherParams'].get('useMixedPrecision', False):
        train_args['amp'] = True
    
    # 学习率策略
    lr_strategy = config['parameters'].get('learningRateStrategy', '').lower()
    if lr_strategy == '余弦衰减':
        train_args['cos_lr'] = True
    
    # 其他参数...
    
    return train_args
```

### 2. 修改命令行参数解析
将原来的必需参数改为可选参数，优先级：命令行参数 > 配置文件参数

```python
def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='华为NPU多机多卡YOLO训练')
    
    # 配置文件参数
    parser.add_argument('--config', type=str, default='training_config.json', 
                       help='训练配置文件路径')
    parser.add_argument('--task_id', type=str, help='任务ID，用于生成输出目录名')
    
    # 基本训练参数 (可覆盖配置文件)
    parser.add_argument('--model', type=str, help='模型文件路径，优先级高于配置文件')
    parser.add_argument('--data', type=str, help='数据集配置文件，优先级高于配置文件')
    parser.add_argument('--epochs', type=int, help='训练轮次')
    parser.add_argument('--batch', type=int, help='批次大小')
    # ... 其他参数
```

### 3. 修改训练函数签名
简化函数参数，统一使用 `train_args` 字典传递训练配置：

```python
# 修改前
def single_machine_train(args, device):
    train_args = {
        'data': args.data,
        'epochs': args.epochs,
        'batch': args.batch,
        # ...
    }

# 修改后
def single_machine_train(train_args, model_path, device):
    # 直接使用解析好的 train_args
    train_args['device'] = device
    train_args['verbose'] = True
    # ...
```

### 4. 修改主函数逻辑
添加配置文件读取和参数覆盖逻辑：

```python
def main():
    # 解析命令行参数
    args = parse_arguments()
    
    # 读取配置文件
    try:
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✅ 成功加载配置文件: {args.config}")
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {args.config}")
        sys.exit(1)
    
    # 解析训练配置
    train_args = parse_training_config(config, args.data, device)
    
    # 命令行参数覆盖配置文件参数
    if args.model:
        model_path = args.model
    else:
        model_path = config.get('model', {}).get('path', 'yolo11n.pt')
    
    if args.epochs:
        train_args['epochs'] = args.epochs
    # ... 其他参数覆盖
    
    # 调用训练函数
    results = single_machine_train(train_args, model_path, device)
```

## 配置文件格式

创建了示例配置文件 `training_config_npu.json`：

```json
{
  "id": "npu_train_001",
  "model": {
    "path": "yolo11n.pt",
    "type": "detection"
  },
  "training": {
    "dataset": {
      "name": "coco8",
      "path": "coco8.yaml"
    }
  },
  "parameters": {
    "epochs": 100,
    "batchSize": 16,
    "learningRate": 0.01,
    "imageSize": 640,
    "learningRateStrategy": "余弦衰减"
  },
  "otherParams": {
    "optimizer": "SGD",
    "momentum": 0.937,
    "weightDecay": 0.0005,
    "useMixedPrecision": true,
    "useCache": false,
    "earlyStopping": 50,
    "checkpointFreq": 10,
    "labelSmoothing": 0.0,
    "dropout": 0.0,
    "warmupSteps": 1000
  },
  "resources": {
    "workers": 8
  }
}
```

## 使用方式

### 1. 使用配置文件训练
```bash
# 使用默认配置文件 training_config.json
python npu_multi_train.py --device npu:0

# 使用指定配置文件
python npu_multi_train.py --config training_config_npu.json --device npu:0,1,2,3
```

### 2. 命令行参数覆盖配置文件
```bash
# 覆盖训练轮次和批次大小
python npu_multi_train.py --config training_config_npu.json --epochs 200 --batch 32

# 覆盖模型和数据集
python npu_multi_train.py --config training_config_npu.json --model yolo11s.pt --data custom.yaml
```

### 3. 分布式训练
```bash
# 单机多卡
python -m torch.distributed.run --nproc_per_node=4 \
       npu_multi_train.py --config training_config_npu.json --device npu:0,1,2,3

# 多机多卡
python -m torch.distributed.run --nproc_per_node=2 --nnodes=2 \
       --node_rank=0 --master_addr=192.168.1.100 --master_port=29500 \
       npu_multi_train.py --config training_config_npu.json --device npu:0,1
```

## 优势

1. **配置管理**: 训练参数集中管理，便于版本控制和复用
2. **灵活性**: 支持配置文件 + 命令行参数的混合使用
3. **标准化**: 与 `utils/train_yolov8.py` 保持一致的配置格式
4. **可扩展性**: 易于添加新的训练参数和功能
5. **易用性**: 减少命令行参数复杂度，提高使用体验

## 兼容性

- 保持了原有的分布式训练功能
- 支持NPU环境检测和配置
- 兼容华为NPU多机多卡训练
- 保留了训练指标保存和资源监控功能
