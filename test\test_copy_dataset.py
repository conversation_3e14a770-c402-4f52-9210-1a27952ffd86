#!/usr/bin/env python3
"""
测试随机数据集复制脚本
验证目录结构和文件匹配功能
"""

import os
import tempfile
import shutil
from pathlib import Path
import subprocess
import sys

def create_test_dataset(base_dir):
    """创建测试用的数据集结构"""
    base_path = Path(base_dir)
    
    # 创建目录结构
    dirs = [
        "images/train",
        "images/val", 
        "labels/train",
        "labels/val"
    ]
    
    for dir_name in dirs:
        (base_path / dir_name).mkdir(parents=True, exist_ok=True)
    
    # 创建测试图片文件（空文件）
    train_images = [f"train_img_{i:03d}.jpg" for i in range(1, 21)]  # 20张训练图片
    val_images = [f"val_img_{i:03d}.jpg" for i in range(1, 11)]     # 10张验证图片
    
    # 创建图片文件
    for img_name in train_images:
        (base_path / "images" / "train" / img_name).touch()
    
    for img_name in val_images:
        (base_path / "images" / "val" / img_name).touch()
    
    # 创建对应的标签文件（只为部分图片创建标签）
    # 训练集：为前15张图片创建标签
    for i in range(1, 16):
        label_name = f"train_img_{i:03d}.txt"
        label_path = base_path / "labels" / "train" / label_name
        with open(label_path, 'w') as f:
            f.write(f"0 0.5 0.5 0.3 0.3\n")  # 示例YOLO格式标签
    
    # 验证集：为前8张图片创建标签
    for i in range(1, 9):
        label_name = f"val_img_{i:03d}.txt"
        label_path = base_path / "labels" / "val" / label_name
        with open(label_path, 'w') as f:
            f.write(f"1 0.3 0.3 0.2 0.2\n")  # 示例YOLO格式标签
    
    print(f"✅ 创建测试数据集: {base_path}")
    print(f"   📊 训练图片: {len(train_images)} 张，标签: 15 个")
    print(f"   📊 验证图片: {len(val_images)} 张，标签: 8 个")
    print(f"   📊 有效样本: 23 个 (15+8)")

def verify_copied_dataset(target_dir, expected_count):
    """验证复制的数据集"""
    target_path = Path(target_dir)
    
    if not target_path.exists():
        print(f"❌ 目标目录不存在: {target_path}")
        return False
    
    # 检查目录结构
    required_dirs = [
        "images/train",
        "images/val",
        "labels/train", 
        "labels/val"
    ]
    
    for dir_name in required_dirs:
        dir_path = target_path / dir_name
        if not dir_path.exists():
            print(f"❌ 缺少目录: {dir_path}")
            return False
    
    # 统计文件数量
    train_images = list((target_path / "images" / "train").glob("*.jpg"))
    val_images = list((target_path / "images" / "val").glob("*.jpg"))
    train_labels = list((target_path / "labels" / "train").glob("*.txt"))
    val_labels = list((target_path / "labels" / "val").glob("*.txt"))
    
    total_images = len(train_images) + len(val_images)
    total_labels = len(train_labels) + len(val_labels)
    
    print(f"📊 复制结果统计:")
    print(f"   训练图片: {len(train_images)} 张")
    print(f"   验证图片: {len(val_images)} 张")
    print(f"   训练标签: {len(train_labels)} 个")
    print(f"   验证标签: {len(val_labels)} 个")
    print(f"   总计: {total_images} 张图片, {total_labels} 个标签")
    
    # 验证图片和标签匹配
    mismatched = 0
    
    # 检查训练集匹配
    for img_path in train_images:
        label_path = target_path / "labels" / "train" / (img_path.stem + ".txt")
        if not label_path.exists():
            print(f"❌ 缺少标签: {label_path}")
            mismatched += 1
    
    # 检查验证集匹配
    for img_path in val_images:
        label_path = target_path / "labels" / "val" / (img_path.stem + ".txt")
        if not label_path.exists():
            print(f"❌ 缺少标签: {label_path}")
            mismatched += 1
    
    if mismatched == 0:
        print("✅ 所有图片都有对应的标签文件")
    else:
        print(f"❌ 有 {mismatched} 个图片缺少对应标签")
    
    # 验证数量
    if total_images == total_labels == expected_count:
        print(f"✅ 复制数量正确: {expected_count}")
        return True
    else:
        print(f"❌ 复制数量不正确: 期望 {expected_count}, 实际 {total_images}")
        return False

def run_test():
    """运行测试"""
    print("🧪 开始测试随机数据集复制脚本")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试数据集
        source_dir = temp_path / "source"
        target_dir = temp_path / "target"
        
        print("1️⃣ 创建测试数据集...")
        create_test_dataset(source_dir)
        
        print("\n2️⃣ 运行复制脚本...")
        
        # 构建命令
        script_path = Path(__file__).parent / "copy_random_dataset.py"
        cmd = [
            sys.executable, str(script_path),
            "--source", str(source_dir),
            "--target", str(target_dir),
            "--num_samples", "10",
            "--seed", "42"
        ]
        
        try:
            # 运行脚本
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ 脚本执行成功")
                print("📋 脚本输出:")
                print(result.stdout)
            else:
                print("❌ 脚本执行失败")
                print("错误输出:")
                print(result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 脚本执行超时")
            return False
        except Exception as e:
            print(f"❌ 脚本执行异常: {e}")
            return False
        
        print("\n3️⃣ 验证复制结果...")
        success = verify_copied_dataset(target_dir, 10)
        
        if success:
            print("\n🎉 测试通过!")
            return True
        else:
            print("\n💥 测试失败!")
            return False

def main():
    """主函数"""
    print("🔧 随机数据集复制脚本测试工具")
    
    # 检查脚本是否存在
    script_path = Path(__file__).parent / "copy_random_dataset.py"
    if not script_path.exists():
        print(f"❌ 找不到脚本文件: {script_path}")
        return
    
    # 运行测试
    success = run_test()
    
    if success:
        print("\n✅ 所有测试通过!")
        print("💡 可以安全使用 copy_random_dataset.py 脚本")
    else:
        print("\n❌ 测试失败!")
        print("💡 请检查 copy_random_dataset.py 脚本")

if __name__ == "__main__":
    main()
